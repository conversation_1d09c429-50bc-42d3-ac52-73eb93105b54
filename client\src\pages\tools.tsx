import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { 
  FileText, 
  Upload, 
  Download, 
  Scissors, 
  Shield, 
  Combine,
  FileImage,
  Award,
  CreditCard,
  Crown
} from "lucide-react";
import { Link } from "wouter";

export default function Tools() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const user = userData?.user;
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [files, setFiles] = useState<FileList | null>(null);
  const [watermarkText, setWatermarkText] = useState("");
  const [pageRanges, setPageRanges] = useState("1-2,3-5");
  
  // Invoice form data
  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: "",
    date: new Date().toISOString().split('T')[0],
    clientName: "",
    items: [{ description: "", quantity: 1, rate: 0 }]
  });
  
  // Certificate form data
  const [certData, setCertData] = useState({
    name: "",
    course: "",
    date: new Date().toISOString().split('T')[0]
  });

  const maxFileSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB vs 10MB
  const maxFileSizeText = user?.isPremium ? "100MB" : "10MB";

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    const totalSize = Array.from(selectedFiles).reduce((sum, file) => sum + file.size, 0);
    if (totalSize > maxFileSize) {
      toast({
        title: "File size too large",
        description: `Total file size exceeds ${maxFileSizeText} limit. ${user?.isPremium ? '' : 'Upgrade to Premium for larger files.'}`,
        variant: "destructive",
      });
      return;
    }

    setFiles(selectedFiles);
  };

  const processFile = async (endpoint: string, formData: FormData) => {
    setIsProcessing(true);
    try {
      const response = await fetch(endpoint, {
        method: "POST",
        body: formData,
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Processing failed");
      }

      // Download the processed file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = response.headers.get("Content-Disposition")?.split("filename=")[1]?.replace(/"/g, "") || "processed.pdf";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success!",
        description: "Your PDF has been processed and downloaded.",
      });
    } catch (error: any) {
      toast({
        title: "Processing failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleMergePDFs = async () => {
    if (!files || files.length < 2) {
      toast({
        title: "Multiple files required",
        description: "Please select at least 2 PDF files to merge.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    Array.from(files).forEach(file => formData.append("files", file));
    await processFile("/api/pdf/merge", formData);
  };

  const handleSplitPDF = async () => {
    if (!files || files.length !== 1) {
      toast({
        title: "Single file required",
        description: "Please select exactly one PDF file to split.",
        variant: "destructive",
      });
      return;
    }

    try {
      const ranges = pageRanges.split(",").map(range => {
        const [start, end] = range.trim().split("-").map(n => parseInt(n));
        return [start, end || start];
      });

      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("pageRanges", JSON.stringify(ranges));
      await processFile("/api/pdf/split", formData);
    } catch (error) {
      toast({
        title: "Invalid page ranges",
        description: "Please use format like '1-2,3-5' for page ranges.",
        variant: "destructive",
      });
    }
  };

  const handleWatermark = async () => {
    if (!files || files.length !== 1) {
      toast({
        title: "Single file required",
        description: "Please select exactly one PDF file to watermark.",
        variant: "destructive",
      });
      return;
    }

    if (!watermarkText.trim()) {
      toast({
        title: "Watermark text required",
        description: "Please enter the watermark text.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("file", files[0]);
    formData.append("watermarkText", watermarkText);
    await processFile("/api/pdf/watermark", formData);
  };

  const handleCompress = async () => {
    if (!files || files.length !== 1) {
      toast({
        title: "Single file required",
        description: "Please select exactly one PDF file to compress.",
        variant: "destructive",
      });
      return;
    }

    const formData = new FormData();
    formData.append("file", files[0]);
    await processFile("/api/pdf/compress", formData);
  };

  const handleGenerateInvoice = async () => {
    if (!invoiceData.invoiceNumber || !invoiceData.clientName || invoiceData.items.some(item => !item.description)) {
      toast({
        title: "Missing information",
        description: "Please fill in all required invoice fields.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch("/api/generate/invoice", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(invoiceData),
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Invoice generation failed");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `invoice-${invoiceData.invoiceNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Invoice generated!",
        description: "Your invoice has been generated and downloaded.",
      });
    } catch (error) {
      toast({
        title: "Generation failed",
        description: "Failed to generate invoice. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerateCertificate = async () => {
    if (!certData.name || !certData.course) {
      toast({
        title: "Missing information",
        description: "Please fill in all required certificate fields.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch("/api/generate/certificate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(certData),
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Certificate generation failed");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `certificate-${certData.name.replace(/\s+/g, '-')}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Certificate generated!",
        description: "Your certificate has been generated and downloaded.",
      });
    } catch (error) {
      toast({
        title: "Generation failed",
        description: "Failed to generate certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const addInvoiceItem = () => {
    setInvoiceData({
      ...invoiceData,
      items: [...invoiceData.items, { description: "", quantity: 1, rate: 0 }]
    });
  };

  const updateInvoiceItem = (index: number, field: string, value: any) => {
    const newItems = [...invoiceData.items];
    newItems[index] = { ...newItems[index], [field]: value };
    setInvoiceData({ ...invoiceData, items: newItems });
  };

  const removeInvoiceItem = (index: number) => {
    const newItems = invoiceData.items.filter((_, i) => i !== index);
    setInvoiceData({ ...invoiceData, items: newItems });
  };

  if (!user) {
    return <div>Please log in to access PDF tools.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">PDF Tools</h1>
          <p className="text-muted-foreground">
            Process, merge, split, and generate PDF documents
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge variant={user.isPremium ? "default" : "secondary"}>
            {user.isPremium ? "Premium" : "Free"}
          </Badge>
          {!user.isPremium && (
            <Link href="/subscribe">
              <Button size="sm" className="bg-gradient-to-r from-primary to-secondary">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade for {maxFileSizeText} files
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* File Size Limit Notice */}
      <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-orange-600" />
            <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
              File size limit: {maxFileSizeText} per upload
              {!user.isPremium && " (Upgrade to Premium for 100MB files)"}
            </span>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="processing" className="w-full">
        <TabsList className="grid w-full grid-cols-1 md:grid-cols-2">
          <TabsTrigger value="processing">PDF Processing</TabsTrigger>
          <TabsTrigger value="generation">Document Generation</TabsTrigger>
        </TabsList>

        <TabsContent value="processing" className="space-y-6">
          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload PDF Files
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="pdf-files">Select PDF files</Label>
                  <Input
                    id="pdf-files"
                    type="file"
                    accept=".pdf"
                    multiple
                    onChange={handleFileChange}
                    className="mt-2"
                  />
                  {files && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      {files.length} file(s) selected • Total size: {Math.round(Array.from(files).reduce((sum, file) => sum + file.size, 0) / 1024 / 1024 * 100) / 100} MB
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Processing Tools */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Merge PDFs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  Merge PDFs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Combine multiple PDF files into a single document.
                </p>
                <Button 
                  onClick={handleMergePDFs}
                  disabled={isProcessing || !files || files.length < 2}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Merge PDFs
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Split PDF */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Scissors className="h-5 w-5 text-secondary" />
                  Split PDF
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Split a PDF into multiple documents by page ranges.
                  </p>
                  <div>
                    <Label htmlFor="page-ranges">Page ranges (e.g., 1-2,3-5)</Label>
                    <Input
                      id="page-ranges"
                      value={pageRanges}
                      onChange={(e) => setPageRanges(e.target.value)}
                      placeholder="1-2,3-5"
                      className="mt-1"
                    />
                  </div>
                  <Button 
                    onClick={handleSplitPDF}
                    disabled={isProcessing || !files || files.length !== 1}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Scissors className="h-4 w-4 mr-2" />
                        Split PDF
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Add Watermark */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  Add Watermark
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Add a text watermark to protect your PDF.
                  </p>
                  <div>
                    <Label htmlFor="watermark-text">Watermark text</Label>
                    <Input
                      id="watermark-text"
                      value={watermarkText}
                      onChange={(e) => setWatermarkText(e.target.value)}
                      placeholder="CONFIDENTIAL"
                      className="mt-1"
                    />
                  </div>
                  <Button 
                    onClick={handleWatermark}
                    disabled={isProcessing || !files || files.length !== 1 || !watermarkText.trim()}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Shield className="h-4 w-4 mr-2" />
                        Add Watermark
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Combine PDF */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Combine className="h-5 w-5 text-purple-500" />
                  Combine PDF
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Reduce file size while maintaining quality.
                </p>
                <Button 
                  onClick={handleCompress}
                  disabled={isProcessing || !files || files.length !== 1}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Combine className="h-4 w-4 mr-2" />
                      Combine PDF
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="generation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Invoice Generator */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  Invoice Generator
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="invoice-number">Invoice Number</Label>
                      <Input
                        id="invoice-number"
                        value={invoiceData.invoiceNumber}
                        onChange={(e) => setInvoiceData({...invoiceData, invoiceNumber: e.target.value})}
                        placeholder="INV-001"
                      />
                    </div>
                    <div>
                      <Label htmlFor="invoice-date">Date</Label>
                      <Input
                        id="invoice-date"
                        type="date"
                        value={invoiceData.date}
                        onChange={(e) => setInvoiceData({...invoiceData, date: e.target.value})}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="client-name">Client Name</Label>
                    <Input
                      id="client-name"
                      value={invoiceData.clientName}
                      onChange={(e) => setInvoiceData({...invoiceData, clientName: e.target.value})}
                      placeholder="Client Company Name"
                    />
                  </div>

                  <div>
                    <Label>Items</Label>
                    <div className="space-y-2 mt-2">
                      {invoiceData.items.map((item, index) => (
                        <div key={index} className="grid grid-cols-12 gap-2">
                          <Input
                            className="col-span-6"
                            placeholder="Description"
                            value={item.description}
                            onChange={(e) => updateInvoiceItem(index, "description", e.target.value)}
                          />
                          <Input
                            className="col-span-2"
                            type="number"
                            placeholder="Qty"
                            value={item.quantity}
                            onChange={(e) => updateInvoiceItem(index, "quantity", parseInt(e.target.value) || 0)}
                          />
                          <Input
                            className="col-span-3"
                            type="number"
                            step="0.01"
                            placeholder="Rate"
                            value={item.rate}
                            onChange={(e) => updateInvoiceItem(index, "rate", parseFloat(e.target.value) || 0)}
                          />
                          <Button
                            size="sm"
                            variant="outline"
                            className="col-span-1"
                            onClick={() => removeInvoiceItem(index)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                    <Button size="sm" variant="outline" onClick={addInvoiceItem} className="mt-2">
                      Add Item
                    </Button>
                  </div>

                  <Button 
                    onClick={handleGenerateInvoice}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Generate Invoice
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Certificate Generator */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-green-500" />
                  Certificate Generator
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="cert-name">Recipient Name</Label>
                    <Input
                      id="cert-name"
                      value={certData.name}
                      onChange={(e) => setCertData({...certData, name: e.target.value})}
                      placeholder="John Doe"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="cert-course">Course/Achievement</Label>
                    <Input
                      id="cert-course"
                      value={certData.course}
                      onChange={(e) => setCertData({...certData, course: e.target.value})}
                      placeholder="Advanced PDF Processing"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="cert-date">Date</Label>
                    <Input
                      id="cert-date"
                      type="date"
                      value={certData.date}
                      onChange={(e) => setCertData({...certData, date: e.target.value})}
                    />
                  </div>

                  <Button 
                    onClick={handleGenerateCertificate}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Generate Certificate
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
