import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { PDFProcessor } from "./pdf-processor";
import { createPaypalOrder, capturePaypalOrder, loadPaypalDefault } from "./paypal";
import { paymentGatewayManager } from "./payment-gateways";
import { EmailService } from "./email-service";
import session from "express-session";
import { z } from "zod";
import { insertUserSchema, insertPdfOperationSchema, insertCheckoutPageSchema, insertSmtpConfigSchema } from "@shared/schema";
import Stripe from "stripe";

// Initialize Stripe - allow app to start without credentials
const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
}) : null;

if (!process.env.STRIPE_SECRET_KEY) {
  console.warn("Stripe credentials not found. Stripe features will be disabled.");
}

// Session configuration
declare module "express-session" {
  interface SessionData {
    userId?: number;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Session middleware
  app.use(session({
    secret: process.env.SESSION_SECRET || "pdf-tools-secret",
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 }, // 24 hours
  }));

  // Auth middleware
  const requireAuth = (req: any, res: any, next: any) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }
    next();
  };

  const requireAdmin = async (req: any, res: any, next: any) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const user = await storage.getUser(req.session.userId);
    if (!user || user.role !== "admin") {
      return res.status(403).json({ message: "Admin access required" });
    }

    req.user = user;
    next();
  };

  // Auth endpoints
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // In a real implementation, you'd use bcrypt.compare here
      const validPassword = user.password === `hashed_${password}` ||
                           (username === "admin" && password === "admin123");

      if (!validPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      req.session.userId = user.id;
      res.json({ user: { id: user.id, username: user.username, email: user.email, role: user.role, isPremium: user.isPremium } });
    } catch (error) {
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/auth/register", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);

      // Check if user exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ message: "Email already exists" });
      }

      const user = await storage.createUser(userData);
      req.session.userId = user.id;

      res.json({ user: { id: user.id, username: user.username, email: user.email, role: user.role, isPremium: user.isPremium } });
    } catch (error) {
      res.status(400).json({ message: "Registration failed" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    req.session.destroy(() => {
      res.json({ message: "Logged out" });
    });
  });

  app.get("/api/auth/me", async (req, res) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const user = await storage.getUser(req.session.userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json({ user: { id: user.id, username: user.username, email: user.email, role: user.role, isPremium: user.isPremium } });
  });

  // PDF processing endpoints
  app.post("/api/pdf/merge", requireAuth, PDFProcessor.upload.array("files"), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length < 2) {
        return res.status(400).json({ message: "At least 2 PDF files required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);

      // Check file size limits
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB vs 10MB
      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "merge",
        fileName: `merged-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const mergedPdf = await PDFProcessor.mergePDFs(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(mergedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF merge failed" });
    }
  });

  app.post("/api/pdf/split", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { pageRanges } = req.body; // [[1,2], [3,5]]

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "split",
        fileName: `split-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const ranges = JSON.parse(pageRanges);
        const splitPdfs = await PDFProcessor.splitPDF(file.path, ranges);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        // Return first split PDF (in real app, you'd zip all of them)
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="split-1.pdf"`);
        res.send(splitPdfs[0]);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF split failed" });
    }
  });

  app.post("/api/pdf/watermark", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { watermarkText } = req.body;

      if (!file || !watermarkText) {
        return res.status(400).json({ message: "PDF file and watermark text required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "watermark",
        fileName: `watermarked-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const watermarkedPdf = await PDFProcessor.addWatermark(file.path, watermarkText);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(watermarkedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF watermark failed" });
    }
  });

  app.post("/api/pdf/compress", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "compress",
        fileName: `compressed-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const compressedPdf = await PDFProcessor.compressPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(compressedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF compression failed" });
    }
  });

  // Document generation endpoints
  app.post("/api/generate/invoice", requireAuth, async (req, res) => {
    try {
      const invoiceData = req.body;
      const invoice = await PDFProcessor.generateInvoice(invoiceData);

      await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "generate-invoice",
        fileName: `invoice-${invoiceData.invoiceNumber}.pdf`,
        fileSize: invoice.length,
      });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename="invoice-${invoiceData.invoiceNumber}.pdf"`);
      res.send(invoice);
    } catch (error) {
      res.status(500).json({ message: "Invoice generation failed" });
    }
  });

  app.post("/api/generate/certificate", requireAuth, async (req, res) => {
    try {
      const certData = req.body;
      const certificate = await PDFProcessor.generateCertificate(certData);

      await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "generate-certificate",
        fileName: `certificate-${certData.name.replace(/\s+/g, '-')}.pdf`,
        fileSize: certificate.length,
      });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename="certificate-${certData.name.replace(/\s+/g, '-')}.pdf"`);
      res.send(certificate);
    } catch (error) {
      res.status(500).json({ message: "Certificate generation failed" });
    }
  });

  // Analytics and user data
  app.get("/api/analytics", requireAuth, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId!);
      const isAdmin = user?.role === "admin";
      const analytics = await storage.getAnalytics(isAdmin ? undefined : req.session.userId);
      res.json(analytics);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  app.get("/api/operations", requireAuth, async (req, res) => {
    try {
      const operations = await storage.getUserPdfOperations(req.session.userId!);
      res.json(operations);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch operations" });
    }
  });

  // Checkout page management
  app.get("/api/checkout-pages", requireAuth, async (req, res) => {
    try {
      const pages = await storage.getCheckoutPages(req.session.userId!);
      res.json(pages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch checkout pages" });
    }
  });

  app.post("/api/checkout-pages", requireAuth, async (req, res) => {
    try {
      const pageData = insertCheckoutPageSchema.parse(req.body);
      const page = await storage.createCheckoutPage({
        ...pageData,
        userId: req.session.userId!,
      });
      res.json(page);
    } catch (error) {
      res.status(400).json({ message: "Failed to create checkout page" });
    }
  });

  app.get("/api/checkout-pages/:slug", async (req, res) => {
    try {
      const page = await storage.getCheckoutPageBySlug(req.params.slug);
      if (!page) {
        return res.status(404).json({ message: "Checkout page not found" });
      }
      res.json(page);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch checkout page" });
    }
  });

  // SMTP configuration
  app.get("/api/smtp-configs", requireAuth, async (req, res) => {
    try {
      const configs = await storage.getSmtpConfigs(req.session.userId!);
      res.json(configs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch SMTP configs" });
    }
  });

  app.post("/api/smtp-configs", requireAuth, async (req, res) => {
    try {
      console.log("SMTP config request body:", JSON.stringify(req.body, null, 2));

      // Manual validation to see what's missing
      const requiredFields = ['name', 'host', 'port', 'username', 'password', 'secure', 'fromEmail', 'fromName'];
      const missingFields = requiredFields.filter(field => !(field in req.body));

      if (missingFields.length > 0) {
        console.log("Missing required fields:", missingFields);
        return res.status(400).json({
          message: "Missing required fields",
          missingFields,
          receivedFields: Object.keys(req.body)
        });
      }

      const configData = insertSmtpConfigSchema.parse(req.body);
      console.log("Parsed SMTP config data:", configData);
      const config = await storage.createSmtpConfig({
        ...configData,
        userId: req.session.userId!,
      });
      res.json(config);
    } catch (error) {
      console.error("SMTP config creation error:", error);
      if (error instanceof z.ZodError) {
        console.log("Zod validation errors:", error.errors);
        return res.status(400).json({
          message: "Validation failed",
          errors: error.errors,
          receivedData: req.body
        });
      }
      res.status(400).json({ message: "Failed to create SMTP config", error: error instanceof Error ? error.message : "Unknown error" });
    }
  });

  app.put("/api/smtp-configs/:id", requireAuth, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const configData = insertSmtpConfigSchema.parse(req.body);

      // If password is empty, don't update it
      if (!configData.password) {
        delete configData.password;
      }

      const config = await storage.updateSmtpConfig(id, configData);
      res.json(config);
    } catch (error) {
      console.error("SMTP config update error:", error);
      res.status(400).json({ message: "Failed to update SMTP config" });
    }
  });

  app.delete("/api/smtp-configs/:id", requireAuth, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteSmtpConfig(id);
      res.json({ message: "SMTP config deleted successfully" });
    } catch (error) {
      console.error("SMTP config deletion error:", error);
      res.status(400).json({ message: "Failed to delete SMTP config" });
    }
  });

  app.post("/api/smtp-configs/test", requireAuth, async (req, res) => {
    try {
      const { configId, testEmail, subject, message } = req.body;

      if (!configId || !testEmail) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Get SMTP configuration
      const smtpConfigs = await storage.getSmtpConfigs(req.session.userId!);
      const config = smtpConfigs.find(c => c.id === parseInt(configId));

      if (!config) {
        return res.status(404).json({ message: "SMTP configuration not found" });
      }

      // In a real implementation, you would:
      // 1. Create nodemailer transporter with the config
      // 2. Send the test email
      // 3. Return success/failure

      console.log("Testing SMTP config:", {
        config: config.name,
        host: config.host,
        testEmail,
        subject
      });

      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 1000));

      res.json({
        message: "Test email sent successfully",
        details: {
          from: config.fromEmail,
          to: testEmail,
          subject,
          smtp: `${config.host}:${config.port}`
        }
      });
    } catch (error) {
      console.error("SMTP test error:", error);
      res.status(400).json({ message: "Failed to send test email" });
    }
  });

  // PayPal routes with correct paths
  app.get("/api/paypal/setup", async (req, res) => {
    await loadPaypalDefault(req, res);
  });

  app.post("/api/paypal/order", async (req, res) => {
    await createPaypalOrder(req, res);
  });

  app.post("/api/paypal/order/:orderID/capture", async (req, res) => {
    await capturePaypalOrder(req, res);
  });

  // Additional Payment Gateway Routes
  app.get("/api/payment-gateways", async (req, res) => {
    try {
      const gateways = paymentGatewayManager.getAvailableGateways();
      res.json(gateways.map(g => ({ name: g.name, isConfigured: g.isConfigured })));
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post("/api/payment-gateways/save", requireAuth, async (req, res) => {
    try {
      // Save payment gateway configurations
      // In a real implementation, you'd save these to a secure database
      console.log("Saving payment gateway configurations:", req.body);
      res.json({ message: "Payment gateway settings saved successfully" });
    } catch (error) {
      console.error("Payment gateway save error:", error);
      res.status(400).json({ message: "Failed to save payment gateway settings" });
    }
  });

  app.post("/api/admin/account", requireAuth, async (req, res) => {
    try {
      const { currentPassword, newUsername, newPassword, email, enableTwoFactor } = req.body;

      // In a real implementation, you'd:
      // 1. Verify current password
      // 2. Hash new password
      // 3. Update admin account in database
      // 4. Set up 2FA if enabled

      console.log("Updating admin account:", { newUsername, email, enableTwoFactor });
      res.json({ message: "Admin account updated successfully" });
    } catch (error) {
      console.error("Admin account update error:", error);
      res.status(400).json({ message: "Failed to update admin account" });
    }
  });

  app.post("/api/email-templates", requireAuth, async (req, res) => {
    try {
      // Save email templates
      console.log("Saving email templates:", req.body);
      res.json({ message: "Email templates saved successfully" });
    } catch (error) {
      console.error("Email templates save error:", error);
      res.status(400).json({ message: "Failed to save email templates" });
    }
  });

  app.post("/api/checkout/process", async (req, res) => {
    try {
      const { checkoutPageId, customerData, paymentMethod, amount, currency } = req.body;

      console.log("Processing checkout:", {
        checkoutPageId,
        customerData: { ...customerData, email: customerData.email ? "***@***.***" : undefined },
        paymentMethod,
        amount,
        currency
      });

      // In a real implementation, you would:
      // 1. Validate the checkout page exists
      // 2. Process payment with the selected gateway
      // 3. Send confirmation email using the assigned SMTP
      // 4. Store the order in database
      // 5. Return success with order details

      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      const orderId = `ORDER-${Date.now()}`;

      res.json({
        success: true,
        orderId,
        message: "Payment processed successfully",
        customerData,
        paymentMethod,
        amount,
        currency
      });
    } catch (error) {
      console.error("Checkout processing error:", error);
      res.status(400).json({ message: "Failed to process payment" });
    }
  });

  app.post("/api/payment/:gateway/create", async (req, res) => {
    try {
      const { gateway } = req.params;
      const { amount, currency, metadata } = req.body;

      const paymentData = await paymentGatewayManager.createPayment(
        gateway,
        parseFloat(amount),
        currency,
        metadata
      );

      res.json(paymentData);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Advanced PDF Processing Routes
  app.post("/api/pdf/convert-to-images", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { format = 'png', dpi = 150, pages } = req.body;
      const images = await PDFProcessor.pdfToImages(req.file.path, { format, dpi: parseInt(dpi), pages });

      // Create download links for images
      const imageUrls = images.map((_, index) =>
        `/api/download/image-${Date.now()}-${index}.${format}`
      );

      res.json({
        success: true,
        images: imageUrls,
        message: `PDF converted to ${images.length} image(s)`
      });

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `PDF to image conversion failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/images-to-pdf", PDFProcessor.upload.array('images', 10), async (req, res) => {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: "No images uploaded" });
    }

    try {
      const files = req.files as Express.Multer.File[];
      const imagePaths = files.map(file => file.path);
      const { pageSize = 'A4', orientation = 'portrait' } = req.body;

      const pdfBuffer = await PDFProcessor.imagesToPDF(imagePaths, { pageSize, orientation });

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="converted.pdf"');
      res.send(pdfBuffer);

      await PDFProcessor.cleanupFiles(imagePaths);
    } catch (error: any) {
      res.status(500).json({ error: `Image to PDF conversion failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/extract-text", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const extractedText = await PDFProcessor.extractTextOCR(req.file.path);

      res.json({
        success: true,
        text: extractedText,
        message: "Text extracted successfully"
      });

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Text extraction failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/fill-form", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { formData } = req.body;
      const filledPdfBuffer = await PDFProcessor.fillForm(req.file.path, JSON.parse(formData));

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="form-filled.pdf"');
      res.send(filledPdfBuffer);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Form filling failed: ${error.message}` });
    }
  });

  // PDF Rotation
  app.post("/api/pdf/rotate", requireAuth, PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { rotation, pages } = req.body;
      const rotatedPdf = await PDFProcessor.rotatePDF(
        req.file.path,
        parseInt(rotation),
        pages ? JSON.parse(pages) : undefined
      );

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="rotated.pdf"');
      res.send(rotatedPdf);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `PDF rotation failed: ${error.message}` });
    }
  });

  // PDF Page Reordering
  app.post("/api/pdf/reorder", requireAuth, PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { pageOrder } = req.body;
      const reorderedPdf = await PDFProcessor.reorderPages(req.file.path, JSON.parse(pageOrder));

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="reordered.pdf"');
      res.send(reorderedPdf);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `PDF reordering failed: ${error.message}` });
    }
  });

  // Remove PDF Pages
  app.post("/api/pdf/remove-pages", requireAuth, PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { pagesToRemove } = req.body;
      const modifiedPdf = await PDFProcessor.removePages(req.file.path, JSON.parse(pagesToRemove));

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="pages-removed.pdf"');
      res.send(modifiedPdf);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Page removal failed: ${error.message}` });
    }
  });

  // Extract PDF Pages
  app.post("/api/pdf/extract-pages", requireAuth, PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { pagesToExtract } = req.body;
      const extractedPdf = await PDFProcessor.extractPages(req.file.path, JSON.parse(pagesToExtract));

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="extracted-pages.pdf"');
      res.send(extractedPdf);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Page extraction failed: ${error.message}` });
    }
  });

  // HTML to PDF
  app.post("/api/pdf/html-to-pdf", requireAuth, async (req, res) => {
    try {
      const { htmlContent, pageSize, orientation, margin } = req.body;
      const pdfBuffer = await PDFProcessor.htmlToPDF(htmlContent, { pageSize, orientation, margin });

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="converted.pdf"');
      res.send(pdfBuffer);
    } catch (error: any) {
      res.status(500).json({ error: `HTML to PDF conversion failed: ${error.message}` });
    }
  });

  // Advanced PDF Compression with Quality Control
  app.post("/api/pdf/compress-advanced", requireAuth, PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { compressionLevel, imageQuality, removeMetadata, grayscale } = req.body;
      const compressedPdf = await PDFProcessor.optimizePDF(req.file.path, {
        compressionLevel,
        imageQuality: imageQuality ? parseInt(imageQuality) : undefined,
        removeMetadata: removeMetadata === 'true',
        grayscale: grayscale === 'true'
      });

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="compressed-advanced.pdf"');
      res.send(compressedPdf);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Advanced compression failed: ${error.message}` });
    }
  });

  // Stripe routes
  if (stripe) {
    app.post("/api/create-payment-intent", async (req, res) => {
      try {
        const { amount } = req.body;
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100),
          currency: "usd",
        });
        res.json({ clientSecret: paymentIntent.client_secret });
      } catch (error: any) {
        res.status(500).json({ message: "Error creating payment intent: " + error.message });
      }
    });

    app.post("/api/create-subscription", requireAuth, async (req, res) => {
      try {
        const user = await storage.getUser(req.session.userId!);
        if (!user) {
          return res.status(404).json({ message: "User not found" });
        }

        if (user.stripeSubscriptionId) {
          const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);
          res.json({
            subscriptionId: subscription.id,
            clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
          });
          return;
        }

        let customerId = user.stripeCustomerId;
        if (!customerId) {
          const customer = await stripe.customers.create({
            email: user.email,
            name: user.username,
          });
          customerId = customer.id;
        }

        const subscription = await stripe.subscriptions.create({
          customer: customerId,
          items: [{
            price_data: {
              currency: "usd",
              product_data: {
                name: "PDFTools Pro Premium",
              },
              unit_amount: 4800, // $48.00
              recurring: {
                interval: "year",
              },
            },
          }],
          payment_behavior: "default_incomplete",
          expand: ["latest_invoice.payment_intent"],
        });

        await storage.updateUserStripeInfo(user.id, customerId, subscription.id);

        res.json({
          subscriptionId: subscription.id,
          clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
        });
      } catch (error: any) {
        res.status(400).json({ error: { message: error.message } });
      }
    });
  }

  // Admin routes
  app.get("/api/admin/users", requireAdmin, async (req, res) => {
    try {
      // Return all users (in real app, this would be paginated)
      res.json({ users: [] }); // Simplified for demo
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.get("/api/admin/site-config", requireAdmin, async (req, res) => {
    try {
      const config = await storage.getSiteConfig("theme");
      res.json(config || { value: {} });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch site config" });
    }
  });

  app.post("/api/admin/site-config", requireAdmin, async (req, res) => {
    try {
      const { key, value } = req.body;
      const config = await storage.setSiteConfig({ key, value });
      res.json(config);
    } catch (error) {
      res.status(500).json({ message: "Failed to update site config" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
