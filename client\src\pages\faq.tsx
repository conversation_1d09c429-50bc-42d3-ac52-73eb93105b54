import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { HelpCircle, MessageCircle, Mail, Phone } from "lucide-react";
import { <PERSON> } from "wouter";

export default function FAQ() {
  const faqs = [
    {
      category: "General",
      questions: [
        {
          question: "What is PDFTools Pro?",
          answer: "PDFTools Pro is a comprehensive web-based platform for processing PDF documents. We offer tools for merging, splitting, compressing, converting, and securing PDFs, along with custom checkout page creation and payment processing."
        },
        {
          question: "Do I need to install any software?",
          answer: "No! PDFTools Pro is entirely web-based. You can access all features through your browser without downloading or installing any software."
        },
        {
          question: "Is my data secure?",
          answer: "Absolutely. We use enterprise-grade security measures. All files are processed securely and automatically deleted from our servers immediately after processing. We never store your documents."
        },
        {
          question: "What file formats do you support?",
          answer: "We primarily work with PDF files, but also support common image formats (PNG, JPG, JPEG) for conversion to PDF, and HTML for HTML-to-PDF conversion."
        }
      ]
    },
    {
      category: "Pricing & Plans",
      questions: [
        {
          question: "What's included in the free plan?",
          answer: "The free plan includes PDF merge (max 3 files), basic compression, text extraction (3 files/day), 10MB file size limit, and watermarked outputs."
        },
        {
          question: "How much does the premium plan cost?",
          answer: "Premium costs $48/year (just $4/month when billed annually). This includes unlimited access to all features, 100MB file size limit, and priority support."
        },
        {
          question: "Can I cancel my subscription anytime?",
          answer: "Yes, you can cancel your subscription at any time. You'll continue to have access to premium features until the end of your billing period."
        },
        {
          question: "Do you offer refunds?",
          answer: "Yes, we offer a 30-day money-back guarantee. If you're not satisfied with our service, contact us for a full refund."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards, PayPal, Stripe, Paddle, SumUp, Wise, Payoneer, and bank transfers."
        }
      ]
    },
    {
      category: "Features & Usage",
      questions: [
        {
          question: "What's the maximum file size I can upload?",
          answer: "Free users can upload files up to 10MB. Premium users can upload files up to 100MB per file."
        },
        {
          question: "How many files can I process?",
          answer: "Free users have daily limits on certain features (like 3 text extractions per day). Premium users have unlimited processing."
        },
        {
          question: "Can I merge more than 3 PDFs?",
          answer: "Free users can merge up to 3 PDFs at once. Premium users can merge unlimited PDFs in a single operation."
        },
        {
          question: "Do you support password-protected PDFs?",
          answer: "Yes, we can add password protection to PDFs. For processing password-protected PDFs, you'll need to provide the password."
        },
        {
          question: "Can I create custom checkout pages?",
          answer: "Yes! Our checkout page builder allows you to create custom payment pages with your branding, multiple payment gateways, and SMTP routing."
        }
      ]
    },
    {
      category: "Technical",
      questions: [
        {
          question: "Do you have an API?",
          answer: "Yes, premium users get access to our REST API for integrating PDF processing into their applications. API documentation is available in your dashboard."
        },
        {
          question: "Can I integrate with my existing systems?",
          answer: "Yes, through our API and webhook system, you can integrate PDFTools Pro with your existing workflows and applications."
        },
        {
          question: "Do you support SMTP integration?",
          answer: "Yes, you can configure multiple SMTP servers and route emails from different checkout pages through specific SMTP configurations."
        },
        {
          question: "What browsers do you support?",
          answer: "We support all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of your browser."
        }
      ]
    },
    {
      category: "Support",
      questions: [
        {
          question: "How can I get help?",
          answer: "You can contact us through email, live chat, or our support ticket system. Premium users get priority support with faster response times."
        },
        {
          question: "What are your support hours?",
          answer: "We provide 24/7 support through our ticket system. Live chat is available Monday-Friday, 9 AM - 6 PM EST."
        },
        {
          question: "Do you offer training or onboarding?",
          answer: "We provide comprehensive documentation and video tutorials. Enterprise customers can request personalized onboarding sessions."
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-green-500/5">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">PDF</span>
                </div>
                <span className="text-xl font-bold text-foreground">Tools Pro</span>
              </div>
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/auth">
                <Button variant="outline">Sign In</Button>
              </Link>
              <Link href="/auth">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <HelpCircle className="w-16 h-16 text-primary mx-auto mb-6" />
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Find answers to common questions about PDFTools Pro
          </p>
        </div>

        {/* FAQ Sections */}
        <div className="space-y-8">
          {faqs.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Badge variant="outline">{category.category}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  {category.questions.map((faq, faqIndex) => (
                    <AccordionItem key={faqIndex} value={`${categoryIndex}-${faqIndex}`}>
                      <AccordionTrigger className="text-left">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-muted-foreground">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Support Section */}
        <div className="mt-16">
          <Card className="bg-gradient-to-r from-primary/10 to-secondary/10">
            <CardContent className="p-8 text-center">
              <MessageCircle className="w-12 h-12 text-primary mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Still have questions?</h2>
              <p className="text-muted-foreground mb-6">
                Can't find what you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  Email Support
                </Button>
                <Button variant="outline" className="flex items-center gap-2">
                  <MessageCircle className="w-4 h-4" />
                  Live Chat
                </Button>
              </div>
              <div className="mt-6 text-sm text-muted-foreground">
                <p>📧 <EMAIL></p>
                <p>💬 Live chat available Mon-Fri, 9 AM - 6 PM EST</p>
                <p>🎯 Premium users get priority support</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to get started?</h2>
          <p className="text-muted-foreground mb-6">
            Join thousands of users who trust PDFTools Pro for their document processing needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/pricing">
              <Button size="lg" variant="outline">
                View Pricing
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
