import { useState } from "react";
import { useParams } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  CreditCard, 
  Shield, 
  CheckCircle,
  Mail,
  User,
  Building,
  Phone,
  MapPin,
  Globe
} from "lucide-react";

export default function CheckoutPage() {
  const { slug } = useParams();
  const { toast } = useToast();

  const [customerData, setCustomerData] = useState({
    email: "",
    firstName: "",
    lastName: "",
    company: "",
    phone: "",
    address: "",
    city: "",
    country: "",
    zipCode: ""
  });

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");

  // Fetch checkout page data
  const { data: checkoutPage, isLoading } = useQuery({
    queryKey: [`/api/checkout-pages/${slug}`],
    enabled: !!slug,
  });

  // Process payment mutation
  const processPaymentMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/checkout/process", data),
    onSuccess: () => {
      toast({
        title: "Payment successful!",
        description: "Thank you for your purchase. You will receive a confirmation email shortly.",
      });
    },
    onError: () => {
      toast({
        title: "Payment failed",
        description: "There was an error processing your payment. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPaymentMethod) {
      toast({
        title: "Payment method required",
        description: "Please select a payment method to continue.",
        variant: "destructive",
      });
      return;
    }

    // Validate required fields
    const requiredFields = checkoutPage?.requiredFields || {};
    const missingFields = [];

    if (requiredFields.email && !customerData.email) missingFields.push("Email");
    if (requiredFields.firstName && !customerData.firstName) missingFields.push("First Name");
    if (requiredFields.lastName && !customerData.lastName) missingFields.push("Last Name");
    if (requiredFields.company && !customerData.company) missingFields.push("Company");
    if (requiredFields.phone && !customerData.phone) missingFields.push("Phone");
    if (requiredFields.address && !customerData.address) missingFields.push("Address");
    if (requiredFields.city && !customerData.city) missingFields.push("City");
    if (requiredFields.country && !customerData.country) missingFields.push("Country");
    if (requiredFields.zipCode && !customerData.zipCode) missingFields.push("ZIP Code");

    if (missingFields.length > 0) {
      toast({
        title: "Missing required fields",
        description: `Please fill in: ${missingFields.join(", ")}`,
        variant: "destructive",
      });
      return;
    }

    processPaymentMutation.mutate({
      checkoutPageId: checkoutPage.id,
      customerData,
      paymentMethod: selectedPaymentMethod,
      amount: checkoutPage.price,
      currency: checkoutPage.currency
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (!checkoutPage) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Page Not Found</h1>
          <p className="text-gray-600">The checkout page you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  const availablePaymentMethods = Object.entries(checkoutPage.paymentGateways || {})
    .filter(([_, enabled]) => enabled)
    .map(([method, _]) => method);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Order Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-semibold">{checkoutPage.title}</h3>
                  <p className="text-gray-600 mt-2">{checkoutPage.description}</p>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-semibold">
                    <span>Total</span>
                    <span>{checkoutPage.currency} {checkoutPage.price}</span>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-800">
                    <Shield className="h-4 w-4" />
                    <span className="text-sm font-medium">Secure Payment</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Your payment information is encrypted and secure.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Checkout Form */}
          <Card>
            <CardHeader>
              <CardTitle>Complete Your Purchase</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Customer Information */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Customer Information
                  </h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {checkoutPage.requiredFields?.email && (
                      <div className="md:col-span-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={customerData.email}
                          onChange={(e) => setCustomerData({...customerData, email: e.target.value})}
                          required
                        />
                      </div>
                    )}
                    
                    {checkoutPage.requiredFields?.firstName && (
                      <div>
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input
                          id="firstName"
                          value={customerData.firstName}
                          onChange={(e) => setCustomerData({...customerData, firstName: e.target.value})}
                          required
                        />
                      </div>
                    )}
                    
                    {checkoutPage.requiredFields?.lastName && (
                      <div>
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input
                          id="lastName"
                          value={customerData.lastName}
                          onChange={(e) => setCustomerData({...customerData, lastName: e.target.value})}
                          required
                        />
                      </div>
                    )}
                    
                    {checkoutPage.requiredFields?.company && (
                      <div className="md:col-span-2">
                        <Label htmlFor="company">Company</Label>
                        <Input
                          id="company"
                          value={customerData.company}
                          onChange={(e) => setCustomerData({...customerData, company: e.target.value})}
                        />
                      </div>
                    )}
                    
                    {checkoutPage.requiredFields?.phone && (
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          type="tel"
                          value={customerData.phone}
                          onChange={(e) => setCustomerData({...customerData, phone: e.target.value})}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Payment Method
                  </h4>
                  
                  <div className="grid grid-cols-1 gap-3">
                    {availablePaymentMethods.map((method) => (
                      <div
                        key={method}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          selectedPaymentMethod === method
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedPaymentMethod(method)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <input
                              type="radio"
                              name="paymentMethod"
                              value={method}
                              checked={selectedPaymentMethod === method}
                              onChange={() => setSelectedPaymentMethod(method)}
                              className="text-primary"
                            />
                            <span className="font-medium capitalize">
                              {method === 'bankTransfer' ? 'Bank Transfer' : method}
                            </span>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            Available
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  size="lg"
                  disabled={processPaymentMutation.isPending}
                >
                  {processPaymentMutation.isPending ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Complete Purchase - {checkoutPage.currency} {checkoutPage.price}
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
