import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { 
  FileText, 
  Upload, 
  Download, 
  Loader2,
  CheckCircle,
  XCircle
} from "lucide-react";

interface PDFProcessorProps {
  operation: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  endpoint: string;
  fileLimit?: number;
  requiresText?: boolean;
  requiresRanges?: boolean;
  acceptMultiple?: boolean;
}

export default function PDFProcessor({
  operation,
  title,
  description,
  icon: Icon,
  endpoint,
  fileLimit = 1,
  requiresText = false,
  requiresRanges = false,
  acceptMultiple = false
}: PDFProcessorProps) {
  const { toast } = useToast();
  const [files, setFiles] = useState<FileList | null>(null);
  const [textInput, setTextInput] = useState("");
  const [rangeInput, setRangeInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    if (selectedFiles.length > fileLimit) {
      toast({
        title: "Too many files",
        description: `This operation accepts maximum ${fileLimit} file(s).`,
        variant: "destructive",
      });
      return;
    }

    setFiles(selectedFiles);
  };

  const validateInputs = () => {
    if (!files || files.length === 0) {
      toast({
        title: "No files selected",
        description: "Please select at least one PDF file.",
        variant: "destructive",
      });
      return false;
    }

    if (requiresText && !textInput.trim()) {
      toast({
        title: "Missing text input",
        description: "Please enter the required text.",
        variant: "destructive",
      });
      return false;
    }

    if (requiresRanges && !rangeInput.trim()) {
      toast({
        title: "Missing page ranges",
        description: "Please enter page ranges (e.g., 1-2,3-5).",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const simulateProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
    return interval;
  };

  const processFile = async () => {
    if (!validateInputs()) return;

    setIsProcessing(true);
    const progressInterval = simulateProgress();

    try {
      const formData = new FormData();
      
      if (acceptMultiple) {
        Array.from(files!).forEach(file => formData.append("files", file));
      } else {
        formData.append("file", files![0]);
      }

      if (requiresText) {
        formData.append("watermarkText", textInput);
      }

      if (requiresRanges) {
        const ranges = rangeInput.split(",").map(range => {
          const [start, end] = range.trim().split("-").map(n => parseInt(n));
          return [start, end || start];
        });
        formData.append("pageRanges", JSON.stringify(ranges));
      }

      const response = await fetch(endpoint, {
        method: "POST",
        body: formData,
        credentials: "include",
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Processing failed");
      }

      // Download the processed file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = response.headers.get("Content-Disposition")?.split("filename=")[1]?.replace(/"/g, "") || "processed.pdf";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success!",
        description: "Your PDF has been processed and downloaded.",
      });

      // Reset form
      setFiles(null);
      setTextInput("");
      setRangeInput("");
      const fileInput = document.querySelector(`input[type="file"]`) as HTMLInputElement;
      if (fileInput) fileInput.value = "";

    } catch (error: any) {
      clearInterval(progressInterval);
      setProgress(0);
      
      toast({
        title: "Processing failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setTimeout(() => setProgress(0), 2000);
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className="h-5 w-5" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          {description}
        </p>

        {/* File Upload */}
        <div>
          <Label htmlFor={`file-${operation}`}>
            Select PDF file{acceptMultiple ? 's' : ''}
          </Label>
          <Input
            id={`file-${operation}`}
            type="file"
            accept=".pdf"
            multiple={acceptMultiple}
            onChange={handleFileChange}
            className="mt-2"
          />
          {files && (
            <div className="mt-2 text-sm text-muted-foreground">
              {files.length} file(s) selected
            </div>
          )}
        </div>

        {/* Text Input */}
        {requiresText && (
          <div>
            <Label htmlFor={`text-${operation}`}>
              {operation === 'watermark' ? 'Watermark text' : 'Text input'}
            </Label>
            <Input
              id={`text-${operation}`}
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder={operation === 'watermark' ? 'CONFIDENTIAL' : 'Enter text'}
              className="mt-2"
            />
          </div>
        )}

        {/* Range Input */}
        {requiresRanges && (
          <div>
            <Label htmlFor={`ranges-${operation}`}>
              Page ranges (e.g., 1-2,3-5)
            </Label>
            <Input
              id={`ranges-${operation}`}
              value={rangeInput}
              onChange={(e) => setRangeInput(e.target.value)}
              placeholder="1-2,3-5"
              className="mt-2"
            />
          </div>
        )}

        {/* Progress Bar */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Processing...</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* Process Button */}
        <Button 
          onClick={processFile}
          disabled={isProcessing}
          className="w-full"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              {title}
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
