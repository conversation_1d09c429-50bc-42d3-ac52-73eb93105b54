import { mysqlTable, text, serial, int, boolean, timestamp, decimal, json } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = mysqlTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("user"), // user, admin
  isPremium: boolean("is_premium").notNull().default(false),
  premiumExpiresAt: timestamp("premium_expires_at"),
  usageLimit: int("usage_limit").notNull().default(5), // Free tier limit
  usageCount: int("usage_count").notNull().default(0),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  apiKey: text("api_key"),
  customTheme: json("custom_theme"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const pdfOperations = mysqlTable("pdf_operations", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  operation: text("operation").notNull(), // merge, split, compress, convert, watermark, password
  fileName: text("file_name").notNull(),
  fileSize: int("file_size").notNull(),
  status: text("status").notNull().default("processing"), // processing, completed, failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const checkoutPages = mysqlTable("checkout_pages", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  title: text("title").notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull().default("USD"),
  theme: json("theme").notNull(), // colors, fonts, layout
  paymentGateways: json("payment_gateways").notNull(), // stripe, paypal, etc
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const smtpConfigs = mysqlTable("smtp_configs", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  host: text("host").notNull(),
  port: int("port").notNull(),
  username: text("username").notNull(),
  password: text("password").notNull(),
  secure: boolean("secure").notNull().default(true),
  fromEmail: text("from_email").notNull(),
  fromName: text("from_name").notNull(),
  isDefault: boolean("is_default").notNull().default(false),
  routingRules: json("routing_rules"), // For checkout page routing
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const emailTemplates = mysqlTable("email_templates", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  type: text("type").notNull(), // payment_success, payment_failed, subscription_renewed, etc.
  subject: text("subject").notNull(),
  htmlContent: text("html_content").notNull(),
  textContent: text("text_content"),
  variables: json("variables"), // Available template variables
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const webhooks = mysqlTable("webhooks", {
  id: serial("id").primaryKey(),
  userId: int("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  url: text("url").notNull(),
  events: json("events").notNull(), // Array of event types to listen for
  secret: text("secret").notNull(),
  isActive: boolean("is_active").notNull().default(true),
  lastTriggered: timestamp("last_triggered"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const siteConfig = mysqlTable("site_config", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: json("value").notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const payments = mysqlTable("payments", {
  id: serial("id").primaryKey(),
  userId: int("user_id").references(() => users.id),
  checkoutPageId: int("checkout_page_id").references(() => checkoutPages.id),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  currency: text("currency").notNull(),
  gateway: text("gateway").notNull(), // stripe, paypal, etc
  gatewayTransactionId: text("gateway_transaction_id"),
  status: text("status").notNull().default("pending"), // pending, completed, failed, refunded
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  email: true,
  password: true,
});

export const insertPdfOperationSchema = createInsertSchema(pdfOperations).pick({
  operation: true,
  fileName: true,
  fileSize: true,
});

export const insertCheckoutPageSchema = createInsertSchema(checkoutPages).pick({
  name: true,
  slug: true,
  title: true,
  description: true,
  price: true,
  currency: true,
  theme: true,
  paymentGateways: true,
});

export const insertSmtpConfigSchema = createInsertSchema(smtpConfigs).pick({
  name: true,
  host: true,
  port: true,
  username: true,
  password: true,
  secure: true,
  fromEmail: true,
  fromName: true,
  isDefault: true,
  routingRules: true,
});

export const insertEmailTemplateSchema = createInsertSchema(emailTemplates).pick({
  name: true,
  type: true,
  subject: true,
  htmlContent: true,
  textContent: true,
  variables: true,
  isActive: true,
});

export const insertWebhookSchema = createInsertSchema(webhooks).pick({
  name: true,
  url: true,
  events: true,
  secret: true,
  isActive: true,
});

export const insertSiteConfigSchema = createInsertSchema(siteConfig).pick({
  key: true,
  value: true,
});

export const insertPaymentSchema = createInsertSchema(payments).pick({
  checkoutPageId: true,
  amount: true,
  currency: true,
  gateway: true,
  gatewayTransactionId: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertPdfOperation = z.infer<typeof insertPdfOperationSchema>;
export type PdfOperation = typeof pdfOperations.$inferSelect;
export type InsertCheckoutPage = z.infer<typeof insertCheckoutPageSchema>;
export type CheckoutPage = typeof checkoutPages.$inferSelect;
export type InsertSmtpConfig = z.infer<typeof insertSmtpConfigSchema>;
export type SmtpConfig = typeof smtpConfigs.$inferSelect;
export type InsertEmailTemplate = z.infer<typeof insertEmailTemplateSchema>;
export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type InsertWebhook = z.infer<typeof insertWebhookSchema>;
export type Webhook = typeof webhooks.$inferSelect;
export type InsertSiteConfig = z.infer<typeof insertSiteConfigSchema>;
export type SiteConfig = typeof siteConfig.$inferSelect;
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = typeof payments.$inferSelect;
