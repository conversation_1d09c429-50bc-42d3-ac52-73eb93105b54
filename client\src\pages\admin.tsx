import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import {
  <PERSON>,
  Setting<PERSON>,
  Palette,
  Mail,
  BarChart3,
  Shield,
  DollarSign,
  FileText,
  TrendingUp,
  Crown,
  ShoppingCart,
  Eye,
  Cog,
  CreditCard,
  Server,
  Globe,
  Copy,
  ExternalLink,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  AlertCircle
} from "lucide-react";

export default function Admin() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("overview");
  const user = userData?.user;

  const [siteTheme, setSiteTheme] = useState({
    primaryColor: "#635BFF",
    secondaryColor: "#00D4FF",
    successColor: "#32D583",
    backgroundColor: "#FFFFFF",
    textColor: "#1A1F36",
    fontFamily: "Inter"
  });

  // Checkout Page Builder State
  const [checkoutForm, setCheckoutForm] = useState({
    name: "",
    title: "",
    description: "",
    price: "",
    currency: "USD",
    theme: {
      primaryColor: "#635BFF",
      secondaryColor: "#00D4FF",
      backgroundColor: "#FFFFFF",
      textColor: "#1A1F36",
      fontFamily: "Inter",
      layout: "modern"
    },
    paymentGateways: {
      stripe: true,
      paypal: true,
      paddle: false,
      sumup: false,
      wise: false,
      payoneer: false,
      bankTransfer: false
    },
    smtpConfigId: "",
    isActive: true
  });

  // SMTP Configuration State
  const [smtpForm, setSmtpForm] = useState({
    name: "",
    host: "",
    port: "587",
    username: "",
    password: "",
    secure: false,
    fromEmail: "",
    fromName: "",
    isDefault: false,
    routingRules: null
  });

  // Payment Gateway State
  const [paymentGateways, setPaymentGateways] = useState({
    stripe: {
      enabled: false,
      publicKey: "",
      secretKey: "",
      webhookSecret: ""
    },
    paypal: {
      enabled: false,
      clientId: "",
      clientSecret: "",
      environment: "sandbox"
    },
    paddle: {
      enabled: false,
      vendorId: "",
      apiKey: "",
      publicKey: ""
    },
    sumup: {
      enabled: false,
      apiKey: "",
      merchantCode: ""
    },
    wise: {
      enabled: false,
      apiKey: "",
      profileId: ""
    },
    payoneer: {
      enabled: false,
      username: "",
      password: "",
      partnerId: ""
    }
  });

  // SMTP Edit State
  const [editingSmtp, setEditingSmtp] = useState<any>(null);
  const [smtpEditForm, setSmtpEditForm] = useState({
    name: "",
    host: "",
    port: "587",
    username: "",
    password: "",
    secure: false,
    fromEmail: "",
    fromName: "",
    isDefault: false,
    routingRules: null
  });

  // Admin Account Management State
  const [adminForm, setAdminForm] = useState({
    currentPassword: "",
    newUsername: "",
    newPassword: "",
    confirmPassword: "",
    email: "",
    enableTwoFactor: false
  });

  // Email Template State
  const [emailTemplates, setEmailTemplates] = useState({
    purchaseConfirmation: {
      subject: "Purchase Confirmation - {{orderNumber}}",
      htmlContent: "<h2>Thank you for your purchase!</h2>\n<p>Dear {{customerName}},</p>\n<p>Your order has been confirmed. Here are the details:</p>\n<ul>\n  <li>Order Number: {{orderNumber}}</li>\n  <li>Amount: {{amount}} {{currency}}</li>\n  <li>Payment Method: {{paymentMethod}}</li>\n  <li>Date: {{date}}</li>\n</ul>\n<p>Thank you for choosing our service!</p>",
      textContent: "Thank you for your purchase! Order: {{orderNumber}}, Amount: {{amount}} {{currency}}"
    },
    emailConfirmation: {
      subject: "Confirm Your Email Address",
      htmlContent: "<h2>Email Confirmation</h2>\n<p>Please click the link below to confirm your email address:</p>\n<a href=\"{{confirmationLink}}\">Confirm Email</a>\n<p>This link will expire in 24 hours.</p>",
      textContent: "Please confirm your email: {{confirmationLink}}"
    }
  });

  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics"],
    enabled: !!user && user.role === "admin",
  });

  const { data: siteConfig } = useQuery({
    queryKey: ["/api/admin/site-config"],
    enabled: !!user && user.role === "admin",
  });

  const { data: checkoutPages } = useQuery({
    queryKey: ["/api/checkout-pages"],
    enabled: !!user && user.role === "admin",
  });

  const { data: smtpConfigs } = useQuery({
    queryKey: ["/api/smtp-configs"],
    enabled: !!user && user.role === "admin",
  });

  const { data: paymentGatewayStatus } = useQuery({
    queryKey: ["/api/payment-gateways"],
    enabled: !!user && user.role === "admin",
  });

  const updateConfigMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/admin/site-config", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/site-config"] });
      toast({
        title: "Configuration updated!",
        description: "Site configuration has been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "Failed to update site configuration.",
        variant: "destructive",
      });
    },
  });

  const createCheckoutPageMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/checkout-pages", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      toast({
        title: "Checkout page created!",
        description: "Your checkout page has been created successfully.",
      });
      setCheckoutForm({
        name: "",
        title: "",
        description: "",
        price: "",
        currency: "USD",
        theme: {
          primaryColor: "#635BFF",
          secondaryColor: "#00D4FF",
          backgroundColor: "#FFFFFF",
          textColor: "#1A1F36",
          fontFamily: "Inter",
          layout: "modern"
        },
        paymentGateways: {
          stripe: true,
          paypal: true,
          paddle: false,
          sumup: false,
          wise: false,
          payoneer: false,
          bankTransfer: false
        },
        smtpConfigId: "",
        isActive: true
      });
    },
    onError: () => {
      toast({
        title: "Creation failed",
        description: "Failed to create checkout page.",
        variant: "destructive",
      });
    },
  });

  const createSmtpConfigMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/smtp-configs", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration saved!",
        description: "Your SMTP configuration has been saved successfully.",
      });
      setSmtpForm({
        name: "",
        host: "",
        port: "587",
        username: "",
        password: "",
        secure: false,
        fromEmail: "",
        fromName: "",
        isDefault: false,
        routingRules: null
      });
    },
    onError: () => {
      toast({
        title: "Save failed",
        description: "Failed to save SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  const updateSmtpConfigMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => apiRequest("PUT", `/api/smtp-configs/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration updated!",
        description: "Your SMTP configuration has been updated successfully.",
      });
      setEditingSmtp(null);
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "Failed to update SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  const deleteSmtpConfigMutation = useMutation({
    mutationFn: (id: number) => apiRequest("DELETE", `/api/smtp-configs/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration deleted!",
        description: "Your SMTP configuration has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Delete failed",
        description: "Failed to delete SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  const savePaymentGatewaysMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/payment-gateways/save", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payment-gateways"] });
      toast({
        title: "Payment gateways updated!",
        description: "Your payment gateway settings have been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Save failed",
        description: "Failed to save payment gateway settings.",
        variant: "destructive",
      });
    },
  });

  const updateAdminAccountMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/admin/account", data),
    onSuccess: () => {
      toast({
        title: "Account updated!",
        description: "Your admin account has been updated successfully.",
      });
      setAdminForm({
        currentPassword: "",
        newUsername: "",
        newPassword: "",
        confirmPassword: "",
        email: "",
        enableTwoFactor: false
      });
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "Failed to update admin account.",
        variant: "destructive",
      });
    },
  });

  const saveEmailTemplatesMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/email-templates", data),
    onSuccess: () => {
      toast({
        title: "Email templates saved!",
        description: "Your email templates have been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Save failed",
        description: "Failed to save email templates.",
        variant: "destructive",
      });
    },
  });

  const handleThemeUpdate = () => {
    updateConfigMutation.mutate({
      key: "theme",
      value: siteTheme
    });
  };

  const handleCreateCheckoutPage = () => {
    if (!checkoutForm.name || !checkoutForm.title || !checkoutForm.price) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Generate slug from name
    const slug = checkoutForm.name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');

    const formData = {
      ...checkoutForm,
      slug,
      price: parseFloat(checkoutForm.price)
    };

    createCheckoutPageMutation.mutate(formData);
  };

  const handleCreateSmtpConfig = () => {
    if (!smtpForm.name || !smtpForm.host || !smtpForm.username || !smtpForm.password || !smtpForm.fromEmail || !smtpForm.fromName) {
      toast({
        title: "Missing information",
        description: "Please fill in all required SMTP fields including From Email and From Name.",
        variant: "destructive",
      });
      return;
    }

    // Convert port to number
    const formData = {
      ...smtpForm,
      port: parseInt(smtpForm.port)
    };

    createSmtpConfigMutation.mutate(formData);
  };

  const copyPageUrl = (slug: string) => {
    const url = `${window.location.origin}/checkout/${slug}`;
    navigator.clipboard.writeText(url);
    toast({
      title: "URL copied!",
      description: "Checkout page URL has been copied to clipboard.",
    });
  };

  const openPreview = (slug: string) => {
    window.open(`/checkout/${slug}`, '_blank');
  };

  const handleEditSmtp = (config: any) => {
    setEditingSmtp(config);
    setSmtpEditForm({
      name: config.name,
      host: config.host,
      port: config.port.toString(),
      username: config.username,
      password: "", // Don't pre-fill password for security
      secure: config.secure,
      fromEmail: config.fromEmail,
      fromName: config.fromName,
      isDefault: config.isDefault,
      routingRules: config.routingRules
    });
  };

  const handleUpdateSmtp = () => {
    if (!editingSmtp) return;

    const formData = {
      ...smtpEditForm,
      port: parseInt(smtpEditForm.port)
    };

    updateSmtpConfigMutation.mutate({ id: editingSmtp.id, data: formData });
  };

  const handleDeleteSmtp = (id: number) => {
    if (confirm("Are you sure you want to delete this SMTP configuration?")) {
      deleteSmtpConfigMutation.mutate(id);
    }
  };

  const handleSavePaymentGateways = () => {
    savePaymentGatewaysMutation.mutate(paymentGateways);
  };

  const handleUpdateAdminAccount = () => {
    if (adminForm.newPassword !== adminForm.confirmPassword) {
      toast({
        title: "Password mismatch",
        description: "New password and confirmation don't match.",
        variant: "destructive",
      });
      return;
    }

    updateAdminAccountMutation.mutate(adminForm);
  };

  const handleSaveEmailTemplates = () => {
    saveEmailTemplatesMutation.mutate(emailTemplates);
  };

  if (!user || user.role !== "admin") {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Access Denied</h2>
            <p className="text-muted-foreground">
              You need administrator privileges to access this page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  const stats = [
    {
      label: "Total Users",
      value: analytics.totalUsers.toLocaleString(),
      change: "+12%",
      icon: Users,
      color: "text-primary"
    },
    {
      label: "Revenue",
      value: `$${analytics.totalRevenue.toLocaleString()}`,
      change: "+8%",
      icon: DollarSign,
      color: "text-green-500"
    },
    {
      label: "PDF Processed",
      value: analytics.totalPdfProcessed.toLocaleString(),
      change: "+24%",
      icon: FileText,
      color: "text-secondary"
    }
  ];

  const systemStatus = [
    { label: "API Status", status: "Operational", color: "bg-green-500" },
    { label: "PDF Processing", status: "Healthy", color: "bg-green-500" },
    { label: "Database", status: "Connected", color: "bg-green-500" },
    { label: "SMTP Servers", status: `${smtpConfigs?.length || 0} configured`, color: smtpConfigs?.length > 0 ? "bg-green-500" : "bg-orange-500" },
    { label: "Payment Gateways", status: `${paymentGatewayStatus?.filter((g: any) => g.isConfigured).length || 0} active`, color: "bg-green-500" },
    { label: "Checkout Pages", status: `${checkoutPages?.length || 0} created`, color: "bg-blue-500" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            System management and configuration
          </p>
        </div>

        <Badge variant="default" className="bg-green-500">
          <Crown className="h-3 w-3 mr-1" />
          Administrator
        </Badge>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className={`${stat.color} h-5 w-5`} />
                  </div>
                </div>
                <p className="text-sm text-green-500 mt-2 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="checkout">Checkout Builder</TabsTrigger>
              <TabsTrigger value="smtp">SMTP Config</TabsTrigger>
              <TabsTrigger value="payments">Payment Gateways</TabsTrigger>
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.recentOperations.length === 0 ? (
                      <p className="text-muted-foreground text-center py-8">No recent activity</p>
                    ) : (
                      analytics.recentOperations.slice(0, 5).map((operation: any) => (
                        <div key={operation.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <FileText className="text-primary h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-foreground">
                              User processed: {operation.operation} - {operation.fileName}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(operation.createdAt).toLocaleString()}
                            </p>
                          </div>
                          <Badge variant={operation.status === 'completed' ? 'default' : operation.status === 'failed' ? 'destructive' : 'secondary'}>
                            {operation.status}
                          </Badge>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="checkout" className="space-y-6">
              {/* Checkout Page Builder */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Builder Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ShoppingCart className="h-5 w-5" />
                      Create Checkout Page
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="checkout-name">Page Name *</Label>
                        <Input
                          id="checkout-name"
                          value={checkoutForm.name}
                          onChange={(e) => setCheckoutForm({...checkoutForm, name: e.target.value})}
                          placeholder="premium-plan"
                        />
                      </div>
                      <div>
                        <Label htmlFor="checkout-currency">Currency</Label>
                        <Select value={checkoutForm.currency} onValueChange={(value) => setCheckoutForm({...checkoutForm, currency: value})}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="checkout-title">Page Title *</Label>
                      <Input
                        id="checkout-title"
                        value={checkoutForm.title}
                        onChange={(e) => setCheckoutForm({...checkoutForm, title: e.target.value})}
                        placeholder="Upgrade to Premium"
                      />
                    </div>

                    <div>
                      <Label htmlFor="checkout-description">Description</Label>
                      <Textarea
                        id="checkout-description"
                        value={checkoutForm.description}
                        onChange={(e) => setCheckoutForm({...checkoutForm, description: e.target.value})}
                        placeholder="Get access to all premium features..."
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="checkout-price">Price *</Label>
                      <Input
                        id="checkout-price"
                        type="number"
                        value={checkoutForm.price}
                        onChange={(e) => setCheckoutForm({...checkoutForm, price: e.target.value})}
                        placeholder="48.00"
                      />
                    </div>

                    <div>
                      <Label className="mb-3 block">Payment Gateways</Label>
                      <div className="grid grid-cols-2 gap-3">
                        {Object.entries(checkoutForm.paymentGateways).map(([gateway, enabled]) => (
                          <div key={gateway} className="flex items-center space-x-2">
                            <Switch
                              checked={enabled}
                              onCheckedChange={(checked) =>
                                setCheckoutForm({
                                  ...checkoutForm,
                                  paymentGateways: {...checkoutForm.paymentGateways, [gateway]: checked}
                                })
                              }
                            />
                            <Label className="capitalize">{gateway}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="smtp-select">SMTP Configuration</Label>
                      <Select value={checkoutForm.smtpConfigId} onValueChange={(value) => setCheckoutForm({...checkoutForm, smtpConfigId: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select SMTP config" />
                        </SelectTrigger>
                        <SelectContent>
                          {smtpConfigs?.map((config: any) => (
                            <SelectItem key={config.id} value={config.id.toString()}>
                              {config.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      onClick={handleCreateCheckoutPage}
                      disabled={createCheckoutPageMutation.isPending}
                      className="w-full"
                    >
                      {createCheckoutPageMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Checkout Page
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Existing Pages */}
                <Card>
                  <CardHeader>
                    <CardTitle>Existing Checkout Pages</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {checkoutPages?.length === 0 ? (
                        <p className="text-muted-foreground text-center py-8">No checkout pages created yet</p>
                      ) : (
                        checkoutPages?.map((page: any) => (
                          <div key={page.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <h4 className="font-medium">{page.title}</h4>
                              <p className="text-sm text-muted-foreground">{page.currency} {page.price}</p>
                              <p className="text-xs text-muted-foreground">/{page.slug}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => copyPageUrl(page.slug)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => openPreview(page.slug)}
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="smtp" className="space-y-6">
              {/* SMTP Configuration */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* SMTP Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Add SMTP Configuration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="smtp-name">Configuration Name *</Label>
                      <Input
                        id="smtp-name"
                        value={smtpForm.name}
                        onChange={(e) => setSmtpForm({...smtpForm, name: e.target.value})}
                        placeholder="Gmail SMTP"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="smtp-host">SMTP Host *</Label>
                        <Input
                          id="smtp-host"
                          value={smtpForm.host}
                          onChange={(e) => setSmtpForm({...smtpForm, host: e.target.value})}
                          placeholder="smtp.gmail.com"
                        />
                      </div>
                      <div>
                        <Label htmlFor="smtp-port">Port *</Label>
                        <Input
                          id="smtp-port"
                          value={smtpForm.port}
                          onChange={(e) => setSmtpForm({...smtpForm, port: e.target.value})}
                          placeholder="587"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="smtp-username">Username *</Label>
                        <Input
                          id="smtp-username"
                          value={smtpForm.username}
                          onChange={(e) => setSmtpForm({...smtpForm, username: e.target.value})}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label htmlFor="smtp-password">Password *</Label>
                        <Input
                          id="smtp-password"
                          type="password"
                          value={smtpForm.password}
                          onChange={(e) => setSmtpForm({...smtpForm, password: e.target.value})}
                          placeholder="App password"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="smtp-from-email">From Email</Label>
                        <Input
                          id="smtp-from-email"
                          value={smtpForm.fromEmail}
                          onChange={(e) => setSmtpForm({...smtpForm, fromEmail: e.target.value})}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label htmlFor="smtp-from-name">From Name</Label>
                        <Input
                          id="smtp-from-name"
                          value={smtpForm.fromName}
                          onChange={(e) => setSmtpForm({...smtpForm, fromName: e.target.value})}
                          placeholder="PDFTools Pro"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={smtpForm.secure}
                          onCheckedChange={(checked) => setSmtpForm({...smtpForm, secure: checked})}
                        />
                        <Label>Use SSL/TLS</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={smtpForm.isDefault}
                          onCheckedChange={(checked) => setSmtpForm({...smtpForm, isDefault: checked})}
                        />
                        <Label>Set as Default SMTP</Label>
                      </div>
                    </div>

                    <Button
                      onClick={handleCreateSmtpConfig}
                      disabled={createSmtpConfigMutation.isPending}
                      className="w-full"
                    >
                      {createSmtpConfigMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-2" />
                          Save SMTP Configuration
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Existing SMTP Configs */}
                <Card>
                  <CardHeader>
                    <CardTitle>SMTP Configurations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {smtpConfigs?.length === 0 ? (
                        <p className="text-muted-foreground text-center py-8">No SMTP configurations yet</p>
                      ) : (
                        smtpConfigs?.map((config: any) => (
                          <div key={config.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <h4 className="font-medium flex items-center gap-2">
                                {config.name}
                                {config.isDefault && <Badge variant="default" className="text-xs">Default</Badge>}
                              </h4>
                              <p className="text-sm text-muted-foreground">{config.host}:{config.port}</p>
                              <p className="text-xs text-muted-foreground">{config.username}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditSmtp(config)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteSmtp(config.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* SMTP Edit Modal */}
              {editingSmtp && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                  <Card className="w-full max-w-md mx-4">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        Edit SMTP Configuration
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingSmtp(null)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Configuration Name</Label>
                        <Input
                          value={smtpEditForm.name}
                          onChange={(e) => setSmtpEditForm({...smtpEditForm, name: e.target.value})}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>SMTP Host</Label>
                          <Input
                            value={smtpEditForm.host}
                            onChange={(e) => setSmtpEditForm({...smtpEditForm, host: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label>Port</Label>
                          <Input
                            value={smtpEditForm.port}
                            onChange={(e) => setSmtpEditForm({...smtpEditForm, port: e.target.value})}
                          />
                        </div>
                      </div>

                      <div>
                        <Label>Username</Label>
                        <Input
                          value={smtpEditForm.username}
                          onChange={(e) => setSmtpEditForm({...smtpEditForm, username: e.target.value})}
                        />
                      </div>

                      <div>
                        <Label>Password (leave blank to keep current)</Label>
                        <Input
                          type="password"
                          value={smtpEditForm.password}
                          onChange={(e) => setSmtpEditForm({...smtpEditForm, password: e.target.value})}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>From Email</Label>
                          <Input
                            value={smtpEditForm.fromEmail}
                            onChange={(e) => setSmtpEditForm({...smtpEditForm, fromEmail: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label>From Name</Label>
                          <Input
                            value={smtpEditForm.fromName}
                            onChange={(e) => setSmtpEditForm({...smtpEditForm, fromName: e.target.value})}
                          />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={smtpEditForm.secure}
                            onCheckedChange={(checked) => setSmtpEditForm({...smtpEditForm, secure: checked})}
                          />
                          <Label>Use SSL/TLS</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={smtpEditForm.isDefault}
                            onCheckedChange={(checked) => setSmtpEditForm({...smtpEditForm, isDefault: checked})}
                          />
                          <Label>Set as Default SMTP</Label>
                        </div>
                      </div>

                      <div className="flex gap-2 pt-4">
                        <Button
                          onClick={handleUpdateSmtp}
                          disabled={updateSmtpConfigMutation.isPending}
                          className="flex-1"
                        >
                          {updateSmtpConfigMutation.isPending ? "Updating..." : "Update"}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setEditingSmtp(null)}
                          className="flex-1"
                        >
                          Cancel
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>

            <TabsContent value="payments" className="space-y-6">
              {/* Payment Gateway Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Payment Gateway Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Gateway Status Overview */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {paymentGatewayStatus?.map((gateway: any) => (
                        <div key={gateway.name} className="text-center p-4 border rounded-lg">
                          <div className="font-medium capitalize">{gateway.name}</div>
                          <Badge variant={gateway.isConfigured ? "default" : "secondary"} className="mt-2">
                            {gateway.isConfigured ? "Configured" : "Not Configured"}
                          </Badge>
                        </div>
                      ))}
                    </div>

                    {/* Stripe Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Stripe</h3>
                        <Switch
                          checked={paymentGateways.stripe.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              stripe: {...paymentGateways.stripe, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.stripe.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Publishable Key</Label>
                            <Input
                              value={paymentGateways.stripe.publicKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  stripe: {...paymentGateways.stripe, publicKey: e.target.value}
                                })
                              }
                              placeholder="pk_test_..."
                            />
                          </div>
                          <div>
                            <Label>Secret Key</Label>
                            <Input
                              type="password"
                              value={paymentGateways.stripe.secretKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  stripe: {...paymentGateways.stripe, secretKey: e.target.value}
                                })
                              }
                              placeholder="sk_test_..."
                            />
                          </div>
                          <div className="md:col-span-2">
                            <Label>Webhook Secret</Label>
                            <Input
                              value={paymentGateways.stripe.webhookSecret}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  stripe: {...paymentGateways.stripe, webhookSecret: e.target.value}
                                })
                              }
                              placeholder="whsec_..."
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* PayPal Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">PayPal</h3>
                        <Switch
                          checked={paymentGateways.paypal.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              paypal: {...paymentGateways.paypal, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.paypal.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Client ID</Label>
                            <Input
                              value={paymentGateways.paypal.clientId}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paypal: {...paymentGateways.paypal, clientId: e.target.value}
                                })
                              }
                              placeholder="PayPal Client ID"
                            />
                          </div>
                          <div>
                            <Label>Client Secret</Label>
                            <Input
                              type="password"
                              value={paymentGateways.paypal.clientSecret}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paypal: {...paymentGateways.paypal, clientSecret: e.target.value}
                                })
                              }
                              placeholder="PayPal Client Secret"
                            />
                          </div>
                          <div>
                            <Label>Environment</Label>
                            <Select
                              value={paymentGateways.paypal.environment}
                              onValueChange={(value) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paypal: {...paymentGateways.paypal, environment: value}
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="sandbox">Sandbox</SelectItem>
                                <SelectItem value="production">Production</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Paddle Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Paddle</h3>
                        <Switch
                          checked={paymentGateways.paddle.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              paddle: {...paymentGateways.paddle, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.paddle.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Vendor ID</Label>
                            <Input
                              value={paymentGateways.paddle.vendorId}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paddle: {...paymentGateways.paddle, vendorId: e.target.value}
                                })
                              }
                              placeholder="Paddle Vendor ID"
                            />
                          </div>
                          <div>
                            <Label>API Key</Label>
                            <Input
                              type="password"
                              value={paymentGateways.paddle.apiKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paddle: {...paymentGateways.paddle, apiKey: e.target.value}
                                })
                              }
                              placeholder="Paddle API Key"
                            />
                          </div>
                          <div className="md:col-span-2">
                            <Label>Public Key</Label>
                            <Input
                              value={paymentGateways.paddle.publicKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  paddle: {...paymentGateways.paddle, publicKey: e.target.value}
                                })
                              }
                              placeholder="Paddle Public Key"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* SumUp Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">SumUp</h3>
                        <Switch
                          checked={paymentGateways.sumup.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              sumup: {...paymentGateways.sumup, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.sumup.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>API Key</Label>
                            <Input
                              type="password"
                              value={paymentGateways.sumup.apiKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  sumup: {...paymentGateways.sumup, apiKey: e.target.value}
                                })
                              }
                              placeholder="SumUp API Key"
                            />
                          </div>
                          <div>
                            <Label>Merchant Code</Label>
                            <Input
                              value={paymentGateways.sumup.merchantCode}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  sumup: {...paymentGateways.sumup, merchantCode: e.target.value}
                                })
                              }
                              placeholder="SumUp Merchant Code"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Wise Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Wise (TransferWise)</h3>
                        <Switch
                          checked={paymentGateways.wise.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              wise: {...paymentGateways.wise, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.wise.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>API Key</Label>
                            <Input
                              type="password"
                              value={paymentGateways.wise.apiKey}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  wise: {...paymentGateways.wise, apiKey: e.target.value}
                                })
                              }
                              placeholder="Wise API Key"
                            />
                          </div>
                          <div>
                            <Label>Profile ID</Label>
                            <Input
                              value={paymentGateways.wise.profileId}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  wise: {...paymentGateways.wise, profileId: e.target.value}
                                })
                              }
                              placeholder="Wise Profile ID"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Payoneer Configuration */}
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Payoneer</h3>
                        <Switch
                          checked={paymentGateways.payoneer.enabled}
                          onCheckedChange={(checked) =>
                            setPaymentGateways({
                              ...paymentGateways,
                              payoneer: {...paymentGateways.payoneer, enabled: checked}
                            })
                          }
                        />
                      </div>
                      {paymentGateways.payoneer.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <Label>Username</Label>
                            <Input
                              value={paymentGateways.payoneer.username}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  payoneer: {...paymentGateways.payoneer, username: e.target.value}
                                })
                              }
                              placeholder="Payoneer Username"
                            />
                          </div>
                          <div>
                            <Label>Password</Label>
                            <Input
                              type="password"
                              value={paymentGateways.payoneer.password}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  payoneer: {...paymentGateways.payoneer, password: e.target.value}
                                })
                              }
                              placeholder="Payoneer Password"
                            />
                          </div>
                          <div>
                            <Label>Partner ID</Label>
                            <Input
                              value={paymentGateways.payoneer.partnerId}
                              onChange={(e) =>
                                setPaymentGateways({
                                  ...paymentGateways,
                                  payoneer: {...paymentGateways.payoneer, partnerId: e.target.value}
                                })
                              }
                              placeholder="Payoneer Partner ID"
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={handleSavePaymentGateways}
                      disabled={savePaymentGatewaysMutation.isPending}
                      className="w-full"
                    >
                      {savePaymentGatewaysMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Settings className="h-4 w-4 mr-2" />
                          Save Payment Gateway Settings
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="theme" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Site Theme Customization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <Label className="mb-3 block">Brand Colors</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="primary-color">Primary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="primary-color"
                              type="color"
                              value={siteTheme.primaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, primaryColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.primaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, primaryColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="secondary-color">Secondary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="secondary-color"
                              type="color"
                              value={siteTheme.secondaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, secondaryColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.secondaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, secondaryColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="success-color">Success Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="success-color"
                              type="color"
                              value={siteTheme.successColor}
                              onChange={(e) => setSiteTheme({...siteTheme, successColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.successColor}
                              onChange={(e) => setSiteTheme({...siteTheme, successColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="text-color">Text Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="text-color"
                              type="color"
                              value={siteTheme.textColor}
                              onChange={(e) => setSiteTheme({...siteTheme, textColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.textColor}
                              onChange={(e) => setSiteTheme({...siteTheme, textColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <Button
                      onClick={handleThemeUpdate}
                      disabled={updateConfigMutation.isPending}
                      className="w-full"
                    >
                      {updateConfigMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Palette className="h-4 w-4 mr-2" />
                          Update Theme
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              {/* Admin Account Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Admin Account Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Change Username</h4>
                      <div>
                        <Label>Current Password</Label>
                        <Input
                          type="password"
                          value={adminForm.currentPassword}
                          onChange={(e) => setAdminForm({...adminForm, currentPassword: e.target.value})}
                          placeholder="Enter current password"
                        />
                      </div>
                      <div>
                        <Label>New Username</Label>
                        <Input
                          value={adminForm.newUsername}
                          onChange={(e) => setAdminForm({...adminForm, newUsername: e.target.value})}
                          placeholder="Enter new username"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Change Password</h4>
                      <div>
                        <Label>New Password</Label>
                        <Input
                          type="password"
                          value={adminForm.newPassword}
                          onChange={(e) => setAdminForm({...adminForm, newPassword: e.target.value})}
                          placeholder="Enter new password"
                        />
                      </div>
                      <div>
                        <Label>Confirm New Password</Label>
                        <Input
                          type="password"
                          value={adminForm.confirmPassword}
                          onChange={(e) => setAdminForm({...adminForm, confirmPassword: e.target.value})}
                          placeholder="Confirm new password"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Security Settings</h4>
                    <div>
                      <Label>Recovery Email</Label>
                      <Input
                        type="email"
                        value={adminForm.email}
                        onChange={(e) => setAdminForm({...adminForm, email: e.target.value})}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={adminForm.enableTwoFactor}
                        onCheckedChange={(checked) => setAdminForm({...adminForm, enableTwoFactor: checked})}
                      />
                      <Label>Enable Two-Factor Authentication</Label>
                    </div>
                  </div>

                  <Button
                    onClick={handleUpdateAdminAccount}
                    disabled={updateAdminAccountMutation.isPending}
                    className="w-full"
                  >
                    {updateAdminAccountMutation.isPending ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Shield className="h-4 w-4 mr-2" />
                        Update Admin Account
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Email Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Email Templates
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Purchase Confirmation Template */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Purchase Confirmation Email</h4>
                    <div>
                      <Label>Subject</Label>
                      <Input
                        value={emailTemplates.purchaseConfirmation.subject}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          purchaseConfirmation: {
                            ...emailTemplates.purchaseConfirmation,
                            subject: e.target.value
                          }
                        })}
                        placeholder="Purchase Confirmation - {{orderNumber}}"
                      />
                    </div>
                    <div>
                      <Label>HTML Content</Label>
                      <Textarea
                        value={emailTemplates.purchaseConfirmation.htmlContent}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          purchaseConfirmation: {
                            ...emailTemplates.purchaseConfirmation,
                            htmlContent: e.target.value
                          }
                        })}
                        rows={6}
                        placeholder="HTML email content with variables like {{customerName}}, {{orderNumber}}, etc."
                      />
                    </div>
                    <div>
                      <Label>Text Content (Fallback)</Label>
                      <Textarea
                        value={emailTemplates.purchaseConfirmation.textContent}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          purchaseConfirmation: {
                            ...emailTemplates.purchaseConfirmation,
                            textContent: e.target.value
                          }
                        })}
                        rows={3}
                        placeholder="Plain text version"
                      />
                    </div>
                  </div>

                  {/* Email Confirmation Template */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Email Confirmation Template</h4>
                    <div>
                      <Label>Subject</Label>
                      <Input
                        value={emailTemplates.emailConfirmation.subject}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          emailConfirmation: {
                            ...emailTemplates.emailConfirmation,
                            subject: e.target.value
                          }
                        })}
                        placeholder="Confirm Your Email Address"
                      />
                    </div>
                    <div>
                      <Label>HTML Content</Label>
                      <Textarea
                        value={emailTemplates.emailConfirmation.htmlContent}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          emailConfirmation: {
                            ...emailTemplates.emailConfirmation,
                            htmlContent: e.target.value
                          }
                        })}
                        rows={6}
                        placeholder="HTML email content with {{confirmationLink}} variable"
                      />
                    </div>
                    <div>
                      <Label>Text Content (Fallback)</Label>
                      <Textarea
                        value={emailTemplates.emailConfirmation.textContent}
                        onChange={(e) => setEmailTemplates({
                          ...emailTemplates,
                          emailConfirmation: {
                            ...emailTemplates.emailConfirmation,
                            textContent: e.target.value
                          }
                        })}
                        rows={3}
                        placeholder="Plain text version"
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">Available Variables:</h5>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p><strong>Purchase Confirmation:</strong> {{customerName}}, {{orderNumber}}, {{amount}}, {{currency}}, {{paymentMethod}}, {{date}}</p>
                      <p><strong>Email Confirmation:</strong> {{confirmationLink}}, {{customerName}}, {{siteName}}</p>
                    </div>
                  </div>

                  <Button
                    onClick={handleSaveEmailTemplates}
                    disabled={saveEmailTemplatesMutation.isPending}
                    className="w-full"
                  >
                    {saveEmailTemplatesMutation.isPending ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Mail className="h-4 w-4 mr-2" />
                        Save Email Templates
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'checkout' ? 'bg-primary/10 text-primary' : 'hover:bg-primary/5'}`}
                  onClick={() => setActiveTab('checkout')}
                >
                  <ShoppingCart className="h-4 w-4 mr-3" />
                  Checkout Builder
                </Button>
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'smtp' ? 'bg-primary/10 text-primary' : 'hover:bg-primary/5'}`}
                  onClick={() => setActiveTab('smtp')}
                >
                  <Mail className="h-4 w-4 mr-3" />
                  SMTP Configuration
                </Button>
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'payments' ? 'bg-primary/10 text-primary' : 'hover:bg-primary/5'}`}
                  onClick={() => setActiveTab('payments')}
                >
                  <CreditCard className="h-4 w-4 mr-3" />
                  Payment Gateways
                </Button>
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'theme' ? 'bg-primary/10 text-primary' : 'hover:bg-primary/5'}`}
                  onClick={() => setActiveTab('theme')}
                >
                  <Palette className="h-4 w-4 mr-3" />
                  Theme Settings
                </Button>
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'overview' ? 'bg-primary/10 text-primary' : 'hover:bg-primary/5'}`}
                  onClick={() => setActiveTab('overview')}
                >
                  <BarChart3 className="h-4 w-4 mr-3" />
                  View Analytics
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {systemStatus.map((status) => (
                  <div key={status.label} className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{status.label}</span>
                    <Badge className={`${status.color} text-white text-xs`}>
                      {status.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* System Info */}
          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Version:</span>
                  <span className="font-medium">1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Environment:</span>
                  <span className="font-medium">Production</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Uptime:</span>
                  <span className="font-medium">99.9%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Backup:</span>
                  <span className="font-medium">2 hours ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
