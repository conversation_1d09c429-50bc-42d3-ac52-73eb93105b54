import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import { 
  Users, 
  Settings, 
  Palette, 
  Mail, 
  BarChart3,
  Shield,
  DollarSign,
  FileText,
  TrendingUp,
  Crown,
  ShoppingCart,
  Eye,
  Cog
} from "lucide-react";

export default function Admin() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = userData?.user;

  const [siteTheme, setSiteTheme] = useState({
    primaryColor: "#635BFF",
    secondaryColor: "#00D4FF",
    successColor: "#32D583",
    backgroundColor: "#FFFFFF",
    textColor: "#1A1F36",
    fontFamily: "Inter"
  });

  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics"],
    enabled: !!user && user.role === "admin",
  });

  const { data: siteConfig } = useQuery({
    queryKey: ["/api/admin/site-config"],
    enabled: !!user && user.role === "admin",
  });

  const updateConfigMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/admin/site-config", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/site-config"] });
      toast({
        title: "Configuration updated!",
        description: "Site configuration has been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "Failed to update site configuration.",
        variant: "destructive",
      });
    },
  });

  const handleThemeUpdate = () => {
    updateConfigMutation.mutate({
      key: "theme",
      value: siteTheme
    });
  };

  if (!user || user.role !== "admin") {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Access Denied</h2>
            <p className="text-muted-foreground">
              You need administrator privileges to access this page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  const stats = [
    { 
      label: "Total Users", 
      value: analytics.totalUsers.toLocaleString(), 
      change: "+12%", 
      icon: Users,
      color: "text-primary"
    },
    { 
      label: "Revenue", 
      value: `$${analytics.totalRevenue.toLocaleString()}`, 
      change: "+8%", 
      icon: DollarSign,
      color: "text-green-500"
    },
    { 
      label: "PDF Processed", 
      value: analytics.totalPdfProcessed.toLocaleString(), 
      change: "+24%", 
      icon: FileText,
      color: "text-secondary"
    }
  ];

  const systemStatus = [
    { label: "API Status", status: "Operational", color: "bg-green-500" },
    { label: "PDF Processing", status: "Healthy", color: "bg-green-500" },
    { label: "Database", status: "Connected", color: "bg-green-500" },
    { label: "SMTP Servers", status: "2 of 3", color: "bg-orange-500" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            System management and configuration
          </p>
        </div>
        
        <Badge variant="default" className="bg-green-500">
          <Crown className="h-3 w-3 mr-1" />
          Administrator
        </Badge>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.label}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">{stat.label}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className={`${stat.color} h-5 w-5`} />
                  </div>
                </div>
                <p className="text-sm text-green-500 mt-2 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change} from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.recentOperations.length === 0 ? (
                      <p className="text-muted-foreground text-center py-8">No recent activity</p>
                    ) : (
                      analytics.recentOperations.slice(0, 5).map((operation: any) => (
                        <div key={operation.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <FileText className="text-primary h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-foreground">
                              User processed: {operation.operation} - {operation.fileName}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(operation.createdAt).toLocaleString()}
                            </p>
                          </div>
                          <Badge variant={operation.status === 'completed' ? 'default' : operation.status === 'failed' ? 'destructive' : 'secondary'}>
                            {operation.status}
                          </Badge>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">User Management</h3>
                    <p className="text-muted-foreground mb-6">
                      Advanced user management features coming soon.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Current users: {analytics.totalUsers}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="theme" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Site Theme Customization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <Label className="mb-3 block">Brand Colors</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="primary-color">Primary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="primary-color"
                              type="color"
                              value={siteTheme.primaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, primaryColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.primaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, primaryColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="secondary-color">Secondary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="secondary-color"
                              type="color"
                              value={siteTheme.secondaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, secondaryColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.secondaryColor}
                              onChange={(e) => setSiteTheme({...siteTheme, secondaryColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="success-color">Success Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="success-color"
                              type="color"
                              value={siteTheme.successColor}
                              onChange={(e) => setSiteTheme({...siteTheme, successColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.successColor}
                              onChange={(e) => setSiteTheme({...siteTheme, successColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="text-color">Text Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="text-color"
                              type="color"
                              value={siteTheme.textColor}
                              onChange={(e) => setSiteTheme({...siteTheme, textColor: e.target.value})}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={siteTheme.textColor}
                              onChange={(e) => setSiteTheme({...siteTheme, textColor: e.target.value})}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <Button 
                      onClick={handleThemeUpdate}
                      disabled={updateConfigMutation.isPending}
                      className="w-full"
                    >
                      {updateConfigMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Palette className="h-4 w-4 mr-2" />
                          Update Theme
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    System Settings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="text-center py-8">
                      <Cog className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-foreground mb-2">System Configuration</h3>
                      <p className="text-muted-foreground">
                        Advanced system settings and configurations will be available here.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button variant="ghost" className="w-full justify-start bg-primary/5 hover:bg-primary/10">
                  <ShoppingCart className="h-4 w-4 mr-3 text-primary" />
                  View Checkout Pages
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-3" />
                  Manage Users
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <Mail className="h-4 w-4 mr-3" />
                  Configure SMTP
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-3" />
                  View Analytics
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {systemStatus.map((status) => (
                  <div key={status.label} className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{status.label}</span>
                    <Badge className={`${status.color} text-white text-xs`}>
                      {status.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* System Info */}
          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Version:</span>
                  <span className="font-medium">1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Environment:</span>
                  <span className="font-medium">Production</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Uptime:</span>
                  <span className="font-medium">99.9%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Last Backup:</span>
                  <span className="font-medium">2 hours ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
