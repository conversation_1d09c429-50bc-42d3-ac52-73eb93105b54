import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Check, 
  Shield, 
  CreditCard,
  ArrowLeft
} from "lucide-react";
import PayPalButton from "@/components/PayPalButton";

interface CheckoutPageData {
  id: number;
  name: string;
  slug: string;
  title: string;
  description: string;
  price: string;
  currency: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    layout: string;
  };
  paymentGateways: {
    stripe: boolean;
    paypal: boolean;
    bankTransfer: boolean;
  };
  isActive: boolean;
}

export default function Checkout() {
  const [, params] = useRoute("/checkout/:slug");
  const { toast } = useToast();
  const slug = params?.slug;

  const { data: checkoutPage, isLoading, error } = useQuery({
    queryKey: [`/api/checkout-pages/${slug}`],
    enabled: !!slug,
  });

  const [isProcessing, setIsProcessing] = useState(false);

  const handleStripePayment = async () => {
    if (!checkoutPage) return;
    
    setIsProcessing(true);
    try {
      // This would integrate with Stripe Checkout or Payment Elements
      toast({
        title: "Redirecting to Stripe...",
        description: "You'll be redirected to complete your payment.",
      });
      
      // In a real implementation, you'd redirect to Stripe Checkout
      // or use Stripe Elements embedded in the page
      console.log("Redirecting to Stripe for payment:", {
        amount: parseFloat(checkoutPage.price),
        currency: checkoutPage.currency,
        description: checkoutPage.name,
      });
    } catch (error) {
      toast({
        title: "Payment failed",
        description: "Unable to process payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBankTransfer = () => {
    toast({
      title: "Bank Transfer Instructions",
      description: "Please contact support for bank transfer details.",
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (error || !checkoutPage) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Page Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The checkout page you're looking for doesn't exist or has been removed.
            </p>
            <Button asChild>
              <a href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Home
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!checkoutPage.isActive) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-orange-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Page Unavailable</h1>
            <p className="text-muted-foreground mb-6">
              This checkout page is currently inactive. Please contact the seller for assistance.
            </p>
            <Button asChild>
              <a href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Home
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const page = checkoutPage as CheckoutPageData;

  return (
    <div 
      className="min-h-screen flex items-center justify-center p-4"
      style={{
        backgroundColor: page.theme.backgroundColor,
        color: page.theme.textColor,
        fontFamily: page.theme.fontFamily,
      }}
    >
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 
            className="text-3xl font-bold mb-3"
            style={{ color: page.theme.textColor }}
          >
            {page.title}
          </h1>
          {page.description && (
            <p className="text-lg opacity-80">
              {page.description}
            </p>
          )}
        </div>

        {/* Product Card */}
        <Card className="mb-6" style={{ backgroundColor: 'rgba(0,0,0,0.02)' }}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="font-semibold text-lg">{page.name}</span>
              <div className="text-right">
                <div 
                  className="text-3xl font-bold"
                  style={{ color: page.theme.primaryColor }}
                >
                  {page.currency} {parseFloat(page.price).toFixed(2)}
                </div>
                {page.currency === 'USD' && parseFloat(page.price) >= 12 && (
                  <div className="text-sm opacity-60">
                    ~${(parseFloat(page.price) / 12).toFixed(2)}/month
                  </div>
                )}
              </div>
            </div>

            {/* Features - static for demo */}
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span>All premium features included</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span>Unlimited PDF processing</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span>Priority customer support</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span>30-day money-back guarantee</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Buttons */}
        <div className="space-y-3">
          {page.paymentGateways.stripe && (
            <Button
              onClick={handleStripePayment}
              disabled={isProcessing}
              className="w-full text-white"
              style={{ 
                backgroundColor: page.theme.primaryColor,
                borderColor: page.theme.primaryColor 
              }}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Pay with Credit Card
                </>
              )}
            </Button>
          )}

          {page.paymentGateways.paypal && (
            <div className="w-full">
              <PayPalButton 
                amount={page.price} 
                currency={page.currency} 
                intent="sale" 
              />
            </div>
          )}

          {page.paymentGateways.bankTransfer && (
            <Button
              onClick={handleBankTransfer}
              variant="outline"
              className="w-full"
              style={{ 
                borderColor: page.theme.primaryColor,
                color: page.theme.primaryColor 
              }}
            >
              Bank Transfer
            </Button>
          )}
        </div>

        {/* Security Footer */}
        <div className="text-center mt-6 space-y-2">
          <div className="flex items-center justify-center gap-2 text-sm opacity-70">
            <Shield className="h-4 w-4" />
            <span>Secure payment • SSL encrypted</span>
          </div>
          <div className="text-xs opacity-60">
            Powered by PDFTools Pro
          </div>
        </div>
      </div>
    </div>
  );
}
