// Test SMTP configuration creation
const testData = {
  name: "Test Gmail",
  host: "smtp.gmail.com",
  port: 587,
  username: "<EMAIL>",
  password: "testpass",
  secure: false,
  fromEmail: "<EMAIL>",
  fromName: "Test User",
  isDefault: false,
  routingRules: null
};

console.log("Test SMTP data:", JSON.stringify(testData, null, 2));

// Test with fetch
fetch('http://localhost:5000/api/smtp-configs', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Cookie': 'connect.sid=your-session-id' // You'll need to get this from browser
  },
  body: JSON.stringify(testData)
})
.then(response => response.json())
.then(data => console.log('Response:', data))
.catch(error => console.error('Error:', error));
