import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Shield, Zap, Users, Globe, Heart, Award } from "lucide-react";
import { <PERSON> } from "wouter";

export default function About() {
  const values = [
    {
      icon: Shield,
      title: "Security First",
      description: "Your documents are processed securely and deleted immediately after processing. We never store your files."
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Our optimized processing engine handles your PDFs in seconds, not minutes. Built for speed and efficiency."
    },
    {
      icon: Users,
      title: "User-Centric",
      description: "Every feature is designed with our users in mind. Simple, intuitive, and powerful tools for everyone."
    },
    {
      icon: Globe,
      title: "Globally Accessible",
      description: "Available worldwide with multiple payment options and support for international users."
    }
  ];

  const stats = [
    { number: "50,000+", label: "PDFs Processed" },
    { number: "10,000+", label: "Happy Users" },
    { number: "99.9%", label: "Uptime" },
    { number: "24/7", label: "Support" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-green-500/5">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">PDF</span>
                </div>
                <span className="text-xl font-bold text-foreground">Tools Pro</span>
              </div>
            </Link>
            <div className="flex items-center space-x-4">
              <Link href="/auth">
                <Button variant="outline">Sign In</Button>
              </Link>
              <Link href="/auth">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            About PDFTools Pro
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            We're on a mission to make PDF processing simple, secure, and accessible for everyone. 
            From individuals to enterprises, we provide the tools you need to work with PDFs efficiently.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">{stat.number}</div>
              <div className="text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Story Section */}
        <div className="mb-20">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Story</h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  PDFTools Pro was born from a simple frustration: existing PDF tools were either too complex, 
                  too expensive, or didn't respect user privacy. We set out to create a better solution.
                </p>
                <p>
                  Founded in 2024, our team of developers and designers came together with a shared vision: 
                  to build the most user-friendly, secure, and powerful PDF processing platform on the web.
                </p>
                <p>
                  Today, we serve thousands of users worldwide, from students and freelancers to large 
                  enterprises, helping them streamline their document workflows and save valuable time.
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl p-8">
              <div className="text-center">
                <Award className="w-16 h-16 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Award-Winning Platform</h3>
                <p className="text-muted-foreground">
                  Recognized for innovation in document processing and user experience design.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Our Values</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <value.icon className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground text-sm">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Mission Section */}
        <div className="mb-20">
          <Card className="bg-gradient-to-r from-primary to-secondary text-white">
            <CardContent className="p-12 text-center">
              <Heart className="w-16 h-16 mx-auto mb-6 text-white/90" />
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                To democratize document processing by providing powerful, secure, and easy-to-use PDF tools 
                that help people and businesses work more efficiently, regardless of their technical expertise.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Team Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Meet the Team</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary">JD</span>
                </div>
                <h3 className="font-semibold mb-1">John Doe</h3>
                <p className="text-muted-foreground text-sm mb-3">CEO & Founder</p>
                <p className="text-sm text-muted-foreground">
                  Former software engineer with 10+ years in document processing and SaaS platforms.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-secondary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-secondary">JS</span>
                </div>
                <h3 className="font-semibold mb-1">Jane Smith</h3>
                <p className="text-muted-foreground text-sm mb-3">CTO</p>
                <p className="text-sm text-muted-foreground">
                  Expert in cloud architecture and security, ensuring our platform is fast and secure.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-20 h-20 bg-green-500/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl font-bold text-green-500">MB</span>
                </div>
                <h3 className="font-semibold mb-1">Mike Brown</h3>
                <p className="text-muted-foreground text-sm mb-3">Head of Design</p>
                <p className="text-sm text-muted-foreground">
                  UX/UI designer passionate about creating intuitive and beautiful user experiences.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-muted/30 rounded-2xl p-12">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Experience the Difference?
            </h2>
            <p className="text-muted-foreground mb-8 text-lg">
              Join our community and see why thousands choose PDFTools Pro
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button size="lg" className="text-lg px-8 py-6">
                  Get Started Free
                </Button>
              </Link>
              <Link href="/pricing">
                <Button size="lg" variant="outline" className="text-lg px-8 py-6">
                  View Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
