import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import { 
  ShoppingCart, 
  Eye, 
  Plus, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Trash2
} from "lucide-react";

interface CheckoutPage {
  id: number;
  name: string;
  slug: string;
  title: string;
  description: string;
  price: string;
  currency: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    layout: string;
  };
  paymentGateways: {
    stripe: boolean;
    paypal: boolean;
    bankTransfer: boolean;
  };
  isActive: boolean;
  createdAt: string;
}

export default function CheckoutBuilder() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = userData?.user;

  const [isCreating, setIsCreating] = useState(false);
  const [selectedPage, setSelectedPage] = useState<CheckoutPage | null>(null);
  const [previewMode, setPreviewMode] = useState(false);

  // Form state for new/edit checkout page
  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    title: "",
    description: "",
    price: "",
    currency: "USD",
    theme: {
      primaryColor: "#635BFF",
      secondaryColor: "#00D4FF",
      backgroundColor: "#FFFFFF",
      textColor: "#1A1F36",
      fontFamily: "Inter",
      layout: "centered"
    },
    paymentGateways: {
      stripe: true,
      paypal: true,
      bankTransfer: false
    }
  });

  const { data: checkoutPages, isLoading } = useQuery({
    queryKey: ["/api/checkout-pages"],
    enabled: !!user,
  });

  const createPageMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/checkout-pages", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      setIsCreating(false);
      resetForm();
      toast({
        title: "Checkout page created!",
        description: "Your new checkout page is ready to use.",
      });
    },
    onError: () => {
      toast({
        title: "Failed to create page",
        description: "Please check your information and try again.",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      title: "",
      description: "",
      price: "",
      currency: "USD",
      theme: {
        primaryColor: "#635BFF",
        secondaryColor: "#00D4FF",
        backgroundColor: "#FFFFFF",
        textColor: "#1A1F36",
        fontFamily: "Inter",
        layout: "centered"
      },
      paymentGateways: {
        stripe: true,
        paypal: true,
        bankTransfer: false
      }
    });
    setSelectedPage(null);
  };

  const generateSlug = (name: string) => {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
  };

  const handleNameChange = (name: string) => {
    setFormData({
      ...formData,
      name,
      slug: generateSlug(name)
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.title || !formData.price) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    createPageMutation.mutate(formData);
  };

  const copyPageUrl = (slug: string) => {
    const url = `${window.location.origin}/checkout/${slug}`;
    navigator.clipboard.writeText(url);
    toast({
      title: "URL copied!",
      description: "Checkout page URL has been copied to clipboard.",
    });
  };

  const openPreview = (slug: string) => {
    window.open(`/checkout/${slug}`, '_blank');
  };

  if (!user) {
    return <div>Please log in to access the checkout builder.</div>;
  }

  if (!user.isPremium) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-4">Checkout Page Builder</h1>
          <Card className="border-primary/20 bg-primary/5">
            <CardContent className="p-8">
              <ShoppingCart className="h-16 w-16 text-primary mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-foreground mb-2">Premium Feature</h2>
              <p className="text-muted-foreground mb-6">
                Create custom checkout pages with our visual builder. Upgrade to Premium to unlock this feature.
              </p>
              <Button className="bg-gradient-to-r from-primary to-secondary" asChild>
                <a href="/subscribe">Upgrade to Premium</a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Checkout Page Builder</h1>
          <p className="text-muted-foreground">
            Create beautiful, conversion-optimized checkout pages
          </p>
        </div>
        
        <Button onClick={() => setIsCreating(true)} disabled={isCreating}>
          <Plus className="h-4 w-4 mr-2" />
          New Checkout Page
        </Button>
      </div>

      {isCreating ? (
        /* Create/Edit Form */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Page Builder</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="basic">Basic</TabsTrigger>
                    <TabsTrigger value="design">Design</TabsTrigger>
                    <TabsTrigger value="payment">Payment</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div>
                      <Label htmlFor="page-name">Page Name *</Label>
                      <Input
                        id="page-name"
                        value={formData.name}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Premium Plan Checkout"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="page-slug">URL Slug</Label>
                      <Input
                        id="page-slug"
                        value={formData.slug}
                        onChange={(e) => setFormData({...formData, slug: e.target.value})}
                        placeholder="premium-plan"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Will be available at: /checkout/{formData.slug}
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="page-title">Page Title *</Label>
                      <Input
                        id="page-title"
                        value={formData.title}
                        onChange={(e) => setFormData({...formData, title: e.target.value})}
                        placeholder="Upgrade to Premium"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="page-description">Description</Label>
                      <Textarea
                        id="page-description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        placeholder="Unlock all features and process unlimited PDFs"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="price">Price *</Label>
                        <Input
                          id="price"
                          type="number"
                          step="0.01"
                          value={formData.price}
                          onChange={(e) => setFormData({...formData, price: e.target.value})}
                          placeholder="48.00"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency">Currency</Label>
                        <Select 
                          value={formData.currency} 
                          onValueChange={(value) => setFormData({...formData, currency: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="design" className="space-y-4">
                    <div>
                      <Label className="mb-3 block">Brand Colors</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="primary-color">Primary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="primary-color"
                              type="color"
                              value={formData.theme.primaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, primaryColor: e.target.value}
                              })}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={formData.theme.primaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, primaryColor: e.target.value}
                              })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="secondary-color">Secondary Color</Label>
                          <div className="flex items-center gap-2">
                            <Input
                              id="secondary-color"
                              type="color"
                              value={formData.theme.secondaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, secondaryColor: e.target.value}
                              })}
                              className="w-12 h-10 p-1"
                            />
                            <Input
                              value={formData.theme.secondaryColor}
                              onChange={(e) => setFormData({
                                ...formData,
                                theme: {...formData.theme, secondaryColor: e.target.value}
                              })}
                              className="flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="font-family">Typography</Label>
                      <Select 
                        value={formData.theme.fontFamily} 
                        onValueChange={(value) => setFormData({
                          ...formData,
                          theme: {...formData.theme, fontFamily: value}
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="SF Pro Display">SF Pro Display</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Open Sans">Open Sans</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="layout">Layout Style</Label>
                      <Select 
                        value={formData.theme.layout} 
                        onValueChange={(value) => setFormData({
                          ...formData,
                          theme: {...formData.theme, layout: value}
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="centered">Centered</SelectItem>
                          <SelectItem value="split">Split Screen</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="payment" className="space-y-4">
                    <div>
                      <Label className="mb-3 block">Payment Gateways</Label>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <CreditCard className="h-4 w-4" />
                            <span>Stripe</span>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.stripe}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, stripe: checked}
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <CreditCard className="h-4 w-4" />
                            <span>PayPal</span>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.paypal}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, paypal: checked}
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <CreditCard className="h-4 w-4" />
                            <span>Bank Transfer</span>
                          </div>
                          <Switch
                            checked={formData.paymentGateways.bankTransfer}
                            onCheckedChange={(checked) => setFormData({
                              ...formData,
                              paymentGateways: {...formData.paymentGateways, bankTransfer: checked}
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex gap-2">
                  <Button type="submit" disabled={createPageMutation.isPending} className="flex-1">
                    {createPageMutation.isPending ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Create Page
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => {setIsCreating(false); resetForm();}}>
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Live Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Live Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-muted p-3 border-b">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div className="flex-1 bg-background rounded px-3 py-1 text-xs text-muted-foreground ml-3">
                      checkout.pdftools.com/{formData.slug || 'your-page'}
                    </div>
                  </div>
                </div>

                <div 
                  className="p-8"
                  style={{
                    backgroundColor: formData.theme.backgroundColor,
                    color: formData.theme.textColor,
                    fontFamily: formData.theme.fontFamily,
                  }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold mb-2">
                      {formData.title || "Page Title"}
                    </h2>
                    <p className="text-muted-foreground">
                      {formData.description || "Page description"}
                    </p>
                  </div>

                  <div className="bg-muted rounded-lg p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="font-medium">{formData.name || "Product Name"}</span>
                      <span className="text-2xl font-bold">
                        {formData.currency} {formData.price || "0.00"}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {formData.paymentGateways.stripe && (
                      <Button 
                        className="w-full" 
                        style={{ backgroundColor: formData.theme.primaryColor }}
                      >
                        Pay with Stripe
                      </Button>
                    )}
                    {formData.paymentGateways.paypal && (
                      <Button className="w-full bg-blue-600 hover:bg-blue-700">
                        Pay with PayPal
                      </Button>
                    )}
                    {formData.paymentGateways.bankTransfer && (
                      <Button variant="outline" className="w-full">
                        Bank Transfer
                      </Button>
                    )}
                  </div>

                  <p className="text-xs text-center mt-6 text-muted-foreground">
                    Secure payment • 30-day money-back guarantee
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Pages List */
        <div className="space-y-4">
          {!checkoutPages || checkoutPages.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <ShoppingCart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">No checkout pages yet</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first checkout page to start accepting payments.
                </p>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Page
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {checkoutPages.map((page: CheckoutPage) => (
                <Card key={page.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{page.name}</CardTitle>
                      <Badge variant={page.isActive ? "default" : "secondary"}>
                        {page.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-muted-foreground">{page.description}</p>
                        <p className="text-2xl font-bold text-foreground mt-2">
                          {page.currency} {page.price}
                        </p>
                      </div>

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>/checkout/{page.slug}</span>
                        <span>{new Date(page.createdAt).toLocaleDateString()}</span>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openPreview(page.slug)}
                          className="flex-1"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Preview
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyPageUrl(page.slug)}
                          className="flex-1"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Copy URL
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
