import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useUser } from "@/hooks/use-user";
import {
  ShoppingCart,
  Plus,
  Copy,
  ExternalLink,
  CreditCard,
  Mail,
  Settings,
  Shield,
  <PERSON>,
  Palette,
  Edit,
  Trash2
} from "lucide-react";

export default function CheckoutBuilder() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("builder");
  const user = userData?.user;

  // Form states
  const [checkoutForm, setCheckoutForm] = useState({
    name: "",
    title: "",
    description: "",
    price: "",
    currency: "USD",
    theme: {
      primaryColor: "#635BFF",
      secondaryColor: "#00D4FF",
      backgroundColor: "#FFFFFF",
      textColor: "#1A1F36",
      fontFamily: "Inter",
      layout: "modern"
    },
    paymentGateways: {
      stripe: true,
      paypal: true,
      paddle: false,
      sumup: false,
      wise: false,
      payoneer: false,
      bankTransfer: false
    },
    requiredFields: {
      email: true,
      firstName: true,
      lastName: true,
      company: false,
      phone: false,
      address: false,
      city: false,
      country: false,
      zipCode: false
    },
    smtpConfigId: "",
    isActive: true
  });

  const [paymentGateways, setPaymentGateways] = useState({
    stripe: {
      enabled: false,
      publicKey: "",
      secretKey: "",
      webhookSecret: ""
    },
    paypal: {
      enabled: false,
      clientId: "",
      clientSecret: "",
      environment: "sandbox"
    },
    paddle: {
      enabled: false,
      vendorId: "",
      apiKey: "",
      publicKey: ""
    },
    sumup: {
      enabled: false,
      apiKey: "",
      merchantCode: ""
    },
    wise: {
      enabled: false,
      apiKey: "",
      profileId: ""
    },
    payoneer: {
      enabled: false,
      username: "",
      password: "",
      partnerId: ""
    }
  });

  const [smtpForm, setSmtpForm] = useState({
    name: "",
    host: "",
    port: "587",
    username: "",
    password: "",
    secure: false,
    fromEmail: "",
    fromName: "",
    isDefault: false,
    routingRules: null
  });

  const [editingSmtp, setEditingSmtp] = useState<any>(null);
  const [isEditingSmtp, setIsEditingSmtp] = useState(false);

  // Data queries
  const { data: checkoutPages } = useQuery({
    queryKey: ["/api/checkout-pages"],
    queryFn: () => apiRequest("GET", "/api/checkout-pages"),
    enabled: !!user,
  });

  const { data: smtpConfigs } = useQuery({
    queryKey: ["/api/smtp-configs"],
    queryFn: () => apiRequest("GET", "/api/smtp-configs"),
    enabled: !!user,
  });

  const { data: paymentGatewayStatus } = useQuery({
    queryKey: ["/api/payment-gateways"],
    queryFn: () => apiRequest("GET", "/api/payment-gateways"),
    enabled: !!user,
  });

  // Mutations
  const createCheckoutPageMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/checkout-pages", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/checkout-pages"] });
      toast({
        title: "Checkout page created!",
        description: "Your checkout page has been created successfully.",
      });
      setCheckoutForm({
        name: "",
        title: "",
        description: "",
        price: "",
        currency: "USD",
        theme: {
          primaryColor: "#635BFF",
          secondaryColor: "#00D4FF",
          backgroundColor: "#FFFFFF",
          textColor: "#1A1F36",
          fontFamily: "Inter",
          layout: "modern"
        },
        paymentGateways: {
          stripe: true,
          paypal: true,
          paddle: false,
          sumup: false,
          wise: false,
          payoneer: false,
          bankTransfer: false
        },
        requiredFields: {
          email: true,
          firstName: true,
          lastName: true,
          company: false,
          phone: false,
          address: false,
          city: false,
          country: false,
          zipCode: false
        },
        smtpConfigId: "",
        isActive: true
      });
    },
    onError: () => {
      toast({
        title: "Creation failed",
        description: "Failed to create checkout page.",
        variant: "destructive",
      });
    },
  });

  const createSmtpConfigMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/smtp-configs", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration saved!",
        description: "Your SMTP configuration has been saved successfully.",
      });
      setSmtpForm({
        name: "",
        host: "",
        port: "587",
        username: "",
        password: "",
        secure: false,
        fromEmail: "",
        fromName: "",
        isDefault: false,
        routingRules: null
      });
    },
    onError: () => {
      toast({
        title: "Save failed",
        description: "Failed to save SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  const savePaymentGatewaysMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/payment-gateways/save", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payment-gateways"] });
      toast({
        title: "Payment gateways updated!",
        description: "Your payment gateway settings have been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Save failed",
        description: "Failed to save payment gateway settings.",
        variant: "destructive",
      });
    },
  });

  const updateSmtpConfigMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) => apiRequest("PUT", `/api/smtp-configs/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration updated!",
        description: "Your SMTP configuration has been updated successfully.",
      });
      setEditingSmtp(null);
      setIsEditingSmtp(false);
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "Failed to update SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  const deleteSmtpConfigMutation = useMutation({
    mutationFn: (id: number) => apiRequest("DELETE", `/api/smtp-configs/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/smtp-configs"] });
      toast({
        title: "SMTP configuration deleted!",
        description: "Your SMTP configuration has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Delete failed",
        description: "Failed to delete SMTP configuration.",
        variant: "destructive",
      });
    },
  });

  // Handlers
  const handleCreateCheckoutPage = () => {
    if (!checkoutForm.name || !checkoutForm.title || !checkoutForm.price) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Generate slug from name
    const slug = checkoutForm.name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');

    const formData = {
      name: checkoutForm.name,
      slug,
      title: checkoutForm.title,
      description: checkoutForm.description || "",
      price: checkoutForm.price,
      currency: checkoutForm.currency,
      theme: checkoutForm.theme,
      paymentGateways: checkoutForm.paymentGateways
    };

    createCheckoutPageMutation.mutate(formData);
  };

  const handleCreateSmtpConfig = () => {
    if (!smtpForm.name || !smtpForm.host || !smtpForm.username || !smtpForm.password || !smtpForm.fromEmail || !smtpForm.fromName) {
      toast({
        title: "Missing information",
        description: "Please fill in all required SMTP fields including From Email and From Name.",
        variant: "destructive",
      });
      return;
    }

    // Convert port to number
    const formData = {
      ...smtpForm,
      port: parseInt(smtpForm.port)
    };

    createSmtpConfigMutation.mutate(formData);
  };

  const handleSavePaymentGateways = () => {
    savePaymentGatewaysMutation.mutate(paymentGateways);
  };

  const copyPageUrl = (slug: string) => {
    const url = `${window.location.origin}/checkout/${slug}`;
    navigator.clipboard.writeText(url);
    toast({
      title: "URL copied!",
      description: "Checkout page URL has been copied to clipboard.",
    });
  };

  const openPreview = (slug: string) => {
    window.open(`/checkout/${slug}`, '_blank');
  };

  const handleEditSmtp = (config: any) => {
    setEditingSmtp(config);
    setSmtpForm({
      name: config.name,
      host: config.host,
      port: config.port.toString(),
      username: config.username,
      password: "", // Don't pre-fill password for security
      secure: config.secure,
      fromEmail: config.fromEmail,
      fromName: config.fromName,
      isDefault: config.isDefault,
      routingRules: config.routingRules
    });
    setIsEditingSmtp(true);
  };

  const handleUpdateSmtp = () => {
    if (!editingSmtp) return;

    if (!smtpForm.name || !smtpForm.host || !smtpForm.username || !smtpForm.fromEmail || !smtpForm.fromName) {
      toast({
        title: "Missing information",
        description: "Please fill in all required SMTP fields.",
        variant: "destructive",
      });
      return;
    }

    const formData = {
      ...smtpForm,
      port: parseInt(smtpForm.port)
    };

    updateSmtpConfigMutation.mutate({ id: editingSmtp.id, data: formData });
  };

  const handleDeleteSmtp = (id: number) => {
    if (confirm("Are you sure you want to delete this SMTP configuration?")) {
      deleteSmtpConfigMutation.mutate(id);
    }
  };

  const handleCancelEdit = () => {
    setEditingSmtp(null);
    setIsEditingSmtp(false);
    setSmtpForm({
      name: "",
      host: "",
      port: "587",
      username: "",
      password: "",
      secure: false,
      fromEmail: "",
      fromName: "",
      isDefault: false,
      routingRules: null
    });
  };

  if (!user) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-16 w-16 text-blue-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Authentication Required</h2>
            <p className="text-muted-foreground">
              Please log in to access the Checkout Page Builder.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Checkout Page Builder</h1>
          <p className="text-muted-foreground">
            Create and manage checkout pages with payment gateways and customer fields
          </p>
        </div>

        <Badge variant="default" className="bg-blue-500">
          <ShoppingCart className="h-3 w-3 mr-1" />
          Checkout Builder
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="builder">Page Builder</TabsTrigger>
          <TabsTrigger value="payments">Payment Gateways</TabsTrigger>
          <TabsTrigger value="fields">Customer Fields</TabsTrigger>
          <TabsTrigger value="smtp">SMTP Config</TabsTrigger>
        </TabsList>

        <TabsContent value="builder" className="space-y-6">
          {/* Checkout Page Builder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Builder Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Create Checkout Page
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="checkout-name">Page Name *</Label>
                    <Input
                      id="checkout-name"
                      value={checkoutForm.name}
                      onChange={(e) => setCheckoutForm({...checkoutForm, name: e.target.value})}
                      placeholder="premium-plan"
                    />
                  </div>
                  <div>
                    <Label htmlFor="checkout-currency">Currency</Label>
                    <Select value={checkoutForm.currency} onValueChange={(value) => setCheckoutForm({...checkoutForm, currency: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="checkout-title">Page Title *</Label>
                  <Input
                    id="checkout-title"
                    value={checkoutForm.title}
                    onChange={(e) => setCheckoutForm({...checkoutForm, title: e.target.value})}
                    placeholder="Upgrade to Premium"
                  />
                </div>

                <div>
                  <Label htmlFor="checkout-description">Description</Label>
                  <Textarea
                    id="checkout-description"
                    value={checkoutForm.description}
                    onChange={(e) => setCheckoutForm({...checkoutForm, description: e.target.value})}
                    placeholder="Get access to all premium features..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="checkout-price">Price *</Label>
                  <Input
                    id="checkout-price"
                    type="number"
                    value={checkoutForm.price}
                    onChange={(e) => setCheckoutForm({...checkoutForm, price: e.target.value})}
                    placeholder="48.00"
                  />
                </div>

                <div>
                  <Label htmlFor="smtp-select">SMTP Configuration</Label>
                  <Select value={checkoutForm.smtpConfigId} onValueChange={(value) => setCheckoutForm({...checkoutForm, smtpConfigId: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select SMTP config" />
                    </SelectTrigger>
                    <SelectContent>
                      {smtpConfigs?.map((config: any) => (
                        <SelectItem key={config.id} value={config.id.toString()}>
                          {config.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  onClick={handleCreateCheckoutPage}
                  disabled={createCheckoutPageMutation.isPending}
                  className="w-full"
                >
                  {createCheckoutPageMutation.isPending ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Checkout Page
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Existing Pages */}
            <Card>
              <CardHeader>
                <CardTitle>Existing Checkout Pages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {checkoutPages?.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">No checkout pages created yet</p>
                  ) : (
                    checkoutPages?.map((page: any) => (
                      <div key={page.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{page.title}</h4>
                          <p className="text-sm text-muted-foreground">{page.currency} {page.price}</p>
                          <p className="text-xs text-muted-foreground">/{page.slug}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyPageUrl(page.slug)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openPreview(page.slug)}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          {/* Payment Gateways Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Gateway Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Stripe */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Stripe</h3>
                      <p className="text-sm text-muted-foreground">Accept credit cards and digital wallets</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.stripe.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        stripe: { ...paymentGateways.stripe, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.stripe.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="stripe-public">Publishable Key</Label>
                      <Input
                        id="stripe-public"
                        value={paymentGateways.stripe.publicKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            stripe: { ...paymentGateways.stripe, publicKey: e.target.value }
                          })
                        }
                        placeholder="pk_test_..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="stripe-secret">Secret Key</Label>
                      <Input
                        id="stripe-secret"
                        type="password"
                        value={paymentGateways.stripe.secretKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            stripe: { ...paymentGateways.stripe, secretKey: e.target.value }
                          })
                        }
                        placeholder="sk_test_..."
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="stripe-webhook">Webhook Secret</Label>
                      <Input
                        id="stripe-webhook"
                        value={paymentGateways.stripe.webhookSecret}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            stripe: { ...paymentGateways.stripe, webhookSecret: e.target.value }
                          })
                        }
                        placeholder="whsec_..."
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* PayPal */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">PayPal</h3>
                      <p className="text-sm text-muted-foreground">Accept PayPal payments</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.paypal.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        paypal: { ...paymentGateways.paypal, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.paypal.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="paypal-client-id">Client ID</Label>
                      <Input
                        id="paypal-client-id"
                        value={paymentGateways.paypal.clientId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paypal: { ...paymentGateways.paypal, clientId: e.target.value }
                          })
                        }
                        placeholder="AYjnxKLMCOO..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="paypal-secret">Client Secret</Label>
                      <Input
                        id="paypal-secret"
                        type="password"
                        value={paymentGateways.paypal.clientSecret}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paypal: { ...paymentGateways.paypal, clientSecret: e.target.value }
                          })
                        }
                        placeholder="EHLNXlgs..."
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="paypal-environment">Environment</Label>
                      <Select
                        value={paymentGateways.paypal.environment}
                        onValueChange={(value) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paypal: { ...paymentGateways.paypal, environment: value }
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sandbox">Sandbox</SelectItem>
                          <SelectItem value="production">Production</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              {/* Paddle */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Paddle</h3>
                      <p className="text-sm text-muted-foreground">Merchant of record payments</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.paddle.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        paddle: { ...paymentGateways.paddle, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.paddle.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="paddle-vendor-id">Vendor ID</Label>
                      <Input
                        id="paddle-vendor-id"
                        value={paymentGateways.paddle.vendorId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, vendorId: e.target.value }
                          })
                        }
                        placeholder="12345"
                      />
                    </div>
                    <div>
                      <Label htmlFor="paddle-api-key">API Key</Label>
                      <Input
                        id="paddle-api-key"
                        type="password"
                        value={paymentGateways.paddle.apiKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, apiKey: e.target.value }
                          })
                        }
                        placeholder="API Key"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="paddle-public-key">Public Key</Label>
                      <Input
                        id="paddle-public-key"
                        value={paymentGateways.paddle.publicKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            paddle: { ...paymentGateways.paddle, publicKey: e.target.value }
                          })
                        }
                        placeholder="Public Key"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* SumUp */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">SumUp</h3>
                      <p className="text-sm text-muted-foreground">European payment processing</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.sumup.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        sumup: { ...paymentGateways.sumup, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.sumup.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sumup-api-key">API Key</Label>
                      <Input
                        id="sumup-api-key"
                        type="password"
                        value={paymentGateways.sumup.apiKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            sumup: { ...paymentGateways.sumup, apiKey: e.target.value }
                          })
                        }
                        placeholder="API Key"
                      />
                    </div>
                    <div>
                      <Label htmlFor="sumup-merchant-code">Merchant Code</Label>
                      <Input
                        id="sumup-merchant-code"
                        value={paymentGateways.sumup.merchantCode}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            sumup: { ...paymentGateways.sumup, merchantCode: e.target.value }
                          })
                        }
                        placeholder="Merchant Code"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Wise */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Wise</h3>
                      <p className="text-sm text-muted-foreground">International money transfers</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.wise.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        wise: { ...paymentGateways.wise, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.wise.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="wise-api-key">API Key</Label>
                      <Input
                        id="wise-api-key"
                        type="password"
                        value={paymentGateways.wise.apiKey}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            wise: { ...paymentGateways.wise, apiKey: e.target.value }
                          })
                        }
                        placeholder="API Key"
                      />
                    </div>
                    <div>
                      <Label htmlFor="wise-profile-id">Profile ID</Label>
                      <Input
                        id="wise-profile-id"
                        value={paymentGateways.wise.profileId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            wise: { ...paymentGateways.wise, profileId: e.target.value }
                          })
                        }
                        placeholder="Profile ID"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Payoneer */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Payoneer</h3>
                      <p className="text-sm text-muted-foreground">Global payment platform</p>
                    </div>
                  </div>
                  <Switch
                    checked={paymentGateways.payoneer.enabled}
                    onCheckedChange={(checked) =>
                      setPaymentGateways({
                        ...paymentGateways,
                        payoneer: { ...paymentGateways.payoneer, enabled: checked }
                      })
                    }
                  />
                </div>
                {paymentGateways.payoneer.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="payoneer-username">Username</Label>
                      <Input
                        id="payoneer-username"
                        value={paymentGateways.payoneer.username}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, username: e.target.value }
                          })
                        }
                        placeholder="Username"
                      />
                    </div>
                    <div>
                      <Label htmlFor="payoneer-password">Password</Label>
                      <Input
                        id="payoneer-password"
                        type="password"
                        value={paymentGateways.payoneer.password}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, password: e.target.value }
                          })
                        }
                        placeholder="Password"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="payoneer-partner-id">Partner ID</Label>
                      <Input
                        id="payoneer-partner-id"
                        value={paymentGateways.payoneer.partnerId}
                        onChange={(e) =>
                          setPaymentGateways({
                            ...paymentGateways,
                            payoneer: { ...paymentGateways.payoneer, partnerId: e.target.value }
                          })
                        }
                        placeholder="Partner ID"
                      />
                    </div>
                  </div>
                )}
              </div>

              <Button
                onClick={handleSavePaymentGateways}
                disabled={savePaymentGatewaysMutation.isPending}
                className="w-full"
              >
                {savePaymentGatewaysMutation.isPending ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Settings className="h-4 w-4 mr-2" />
                    Save Payment Gateway Settings
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fields" className="space-y-6">
          {/* Customer Fields Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Required Customer Fields
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(checkoutForm.requiredFields).map(([field, required]) => (
                  <div key={field} className="flex items-center space-x-2">
                    <Switch
                      checked={required}
                      onCheckedChange={(checked) =>
                        setCheckoutForm({
                          ...checkoutForm,
                          requiredFields: {...checkoutForm.requiredFields, [field]: checked}
                        })
                      }
                      disabled={field === 'email' || field === 'firstName'}
                    />
                    <Label className="capitalize">
                      {field === 'firstName' ? 'First Name' :
                       field === 'lastName' ? 'Last Name' :
                       field === 'zipCode' ? 'ZIP Code' :
                       field}
                      {(field === 'email' || field === 'firstName') && ' *'}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                * Email and First Name are always required for purchase processing.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="smtp" className="space-y-6">
          {/* SMTP Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                SMTP Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtp-name">Configuration Name *</Label>
                  <Input
                    id="smtp-name"
                    value={smtpForm.name}
                    onChange={(e) => setSmtpForm({...smtpForm, name: e.target.value})}
                    placeholder="Main SMTP"
                  />
                </div>
                <div>
                  <Label htmlFor="smtp-host">SMTP Host *</Label>
                  <Input
                    id="smtp-host"
                    value={smtpForm.host}
                    onChange={(e) => setSmtpForm({...smtpForm, host: e.target.value})}
                    placeholder="smtp.gmail.com"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtp-port">Port *</Label>
                  <Input
                    id="smtp-port"
                    value={smtpForm.port}
                    onChange={(e) => setSmtpForm({...smtpForm, port: e.target.value})}
                    placeholder="587"
                  />
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <Switch
                    checked={smtpForm.secure}
                    onCheckedChange={(checked) => setSmtpForm({...smtpForm, secure: checked})}
                  />
                  <Label>Use SSL/TLS</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtp-username">Username *</Label>
                  <Input
                    id="smtp-username"
                    value={smtpForm.username}
                    onChange={(e) => setSmtpForm({...smtpForm, username: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="smtp-password">Password *</Label>
                  <Input
                    id="smtp-password"
                    type="password"
                    value={smtpForm.password}
                    onChange={(e) => setSmtpForm({...smtpForm, password: e.target.value})}
                    placeholder="your-app-password"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtp-from-email">From Email *</Label>
                  <Input
                    id="smtp-from-email"
                    value={smtpForm.fromEmail}
                    onChange={(e) => setSmtpForm({...smtpForm, fromEmail: e.target.value})}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="smtp-from-name">From Name *</Label>
                  <Input
                    id="smtp-from-name"
                    value={smtpForm.fromName}
                    onChange={(e) => setSmtpForm({...smtpForm, fromName: e.target.value})}
                    placeholder="Your Company"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={smtpForm.isDefault}
                  onCheckedChange={(checked) => setSmtpForm({...smtpForm, isDefault: checked})}
                />
                <Label>Set as default SMTP configuration</Label>
              </div>

              <div className="flex gap-2">
                {isEditingSmtp ? (
                  <>
                    <Button
                      onClick={handleUpdateSmtp}
                      disabled={updateSmtpConfigMutation.isPending}
                      className="flex-1"
                    >
                      {updateSmtpConfigMutation.isPending ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Settings className="h-4 w-4 mr-2" />
                          Update SMTP Configuration
                        </>
                      )}
                    </Button>
                    <Button
                      onClick={handleCancelEdit}
                      variant="outline"
                      disabled={updateSmtpConfigMutation.isPending}
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={handleCreateSmtpConfig}
                    disabled={createSmtpConfigMutation.isPending}
                    className="w-full"
                  >
                    {createSmtpConfigMutation.isPending ? (
                      <>
                        <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Save SMTP Configuration
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Existing SMTP Configs */}
          {smtpConfigs && Array.isArray(smtpConfigs) && smtpConfigs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Existing SMTP Configurations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {smtpConfigs.map((config: any) => (
                    <div key={config.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{config.name}</h4>
                        <p className="text-sm text-muted-foreground">{config.host}:{config.port}</p>
                        <p className="text-xs text-muted-foreground">{config.fromEmail}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {config.isDefault && (
                          <Badge variant="default">Default</Badge>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditSmtp(config)}
                          disabled={isEditingSmtp}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteSmtp(config.id)}
                          disabled={deleteSmtpConfigMutation.isPending || isEditingSmtp}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
