import multer from "multer";
import path from "path";
import fs from "fs/promises";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";

// Configure multer for file uploads
const upload = multer({
  dest: "uploads/",
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB for premium users, will be validated per user
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "application/pdf") {
      cb(null, true);
    } else {
      cb(new Error("Only PDF files are allowed"));
    }
  },
});

export class PDFProcessor {
  static upload = upload;

  // Advanced PDF to Image conversion
  static async pdfToImages(filePath: string, options: { 
    format?: 'png' | 'jpg' | 'jpeg',
    dpi?: number,
    pages?: string // e.g., "1-3" or "1,3,5"
  } = {}): Promise<Buffer[]> {
    const { format = 'png', dpi = 150, pages } = options;
    
    // In a real implementation, you would use a library like pdf2pic or similar
    // For now, we'll create a placeholder that returns the PDF as a base64 image
    const pdfBuffer = await fs.readFile(filePath);
    
    // This is a simplified version - in production you'd use proper PDF-to-image conversion
    return [pdfBuffer]; // Returns array of image buffers
  }

  // Image to PDF conversion
  static async imagesToPDF(imagePaths: string[], options: {
    pageSize?: 'A4' | 'Letter' | 'Legal',
    orientation?: 'portrait' | 'landscape'
  } = {}): Promise<Buffer> {
    const { pageSize = 'A4', orientation = 'portrait' } = options;
    
    const pdfDoc = await PDFDocument.create();
    
    for (const imagePath of imagePaths) {
      const imageBuffer = await fs.readFile(imagePath);
      const image = imagePath.toLowerCase().endsWith('.png') 
        ? await pdfDoc.embedPng(imageBuffer)
        : await pdfDoc.embedJpg(imageBuffer);
      
      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);
      
      // Scale image to fit page
      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);
      
      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }
    
    return Buffer.from(await pdfDoc.save());
  }

  // OCR Text Extraction
  static async extractTextOCR(filePath: string): Promise<string> {
    // This would integrate with an OCR service like Tesseract.js or cloud OCR
    // For now, return a placeholder
    try {
      // In production, you'd convert PDF to images first, then run OCR
      const pdfBuffer = await fs.readFile(filePath);
      return "OCR extracted text would appear here. This feature requires OCR service integration.";
    } catch (error) {
      throw new Error(`OCR extraction failed: ${error}`);
    }
  }

  // Digital Signature
  static async addDigitalSignature(filePath: string, signatureData: {
    certificate: string,
    privateKey: string,
    reason?: string,
    location?: string
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));
    
    // This is a simplified version - proper digital signatures require cryptographic libraries
    const page = pdfDoc.getPages()[0];
    page.drawText(`Digitally Signed - ${signatureData.reason || 'Document approval'}`, {
      x: 50,
      y: 50,
      size: 10,
    });
    
    return Buffer.from(await pdfDoc.save());
  }

  // Form Filling
  static async fillForm(filePath: string, formData: Record<string, string>): Promise<Buffer> {
    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));
    const form = pdfDoc.getForm();
    
    // Fill form fields
    for (const [fieldName, value] of Object.entries(formData)) {
      try {
        const field = form.getTextField(fieldName);
        field.setText(value);
      } catch (error) {
        console.warn(`Field ${fieldName} not found or not a text field`);
      }
    }
    
    // Flatten form to prevent further editing
    form.flatten();
    
    return Buffer.from(await pdfDoc.save());
  }

  // PDF Optimization
  static async optimizePDF(filePath: string, options: {
    imageQuality?: number,
    removeMetadata?: boolean,
    grayscale?: boolean
  } = {}): Promise<Buffer> {
    const { imageQuality = 80, removeMetadata = true, grayscale = false } = options;
    
    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));
    
    if (removeMetadata) {
      // Remove metadata
      pdfDoc.setTitle('');
      pdfDoc.setAuthor('');
      pdfDoc.setSubject('');
      pdfDoc.setCreator('');
      pdfDoc.setProducer('PDF Tools Service');
    }
    
    return Buffer.from(await pdfDoc.save());
  }

  static async mergePDFs(filePaths: string[]): Promise<Buffer> {
    const mergedPdf = await PDFDocument.create();
    
    for (const filePath of filePaths) {
      const pdfBytes = await fs.readFile(filePath);
      const pdf = await PDFDocument.load(pdfBytes);
      const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
      copiedPages.forEach((page) => mergedPdf.addPage(page));
    }
    
    return Buffer.from(await mergedPdf.save());
  }

  static async splitPDF(filePath: string, pageRanges: number[][]): Promise<Buffer[]> {
    const pdfBytes = await fs.readFile(filePath);
    const sourcePdf = await PDFDocument.load(pdfBytes);
    const results: Buffer[] = [];
    
    for (const range of pageRanges) {
      const newPdf = await PDFDocument.create();
      const [start, end] = range;
      const pages = await newPdf.copyPages(sourcePdf, Array.from({ length: end - start + 1 }, (_, i) => start + i - 1));
      pages.forEach((page) => newPdf.addPage(page));
      
      const pdfBytes = await newPdf.save();
      results.push(Buffer.from(pdfBytes));
    }
    
    return results;
  }

  static async addWatermark(filePath: string, watermarkText: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    
    const pages = pdfDoc.getPages();
    
    pages.forEach((page) => {
      const { width, height } = page.getSize();
      page.drawText(watermarkText, {
        x: width / 2 - (watermarkText.length * 10) / 2,
        y: height / 2,
        size: 50,
        font: helveticaFont,
        color: rgb(0.5, 0.5, 0.5),
        opacity: 0.3,
      });
    });
    
    return Buffer.from(await pdfDoc.save());
  }

  static async addPasswordProtection(filePath: string, password: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);
    
    // Note: pdf-lib doesn't support password protection directly
    // This would require a different library like HummusJS or pdf2pic
    // For now, we'll return the original PDF with a note
    console.log(`Password protection requested for ${filePath} with password: ${password}`);
    
    return Buffer.from(await pdfDoc.save());
  }

  static async compressPDF(filePath: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);
    
    // Basic compression by re-saving
    // More advanced compression would require additional libraries
    return Buffer.from(await pdfDoc.save({ useObjectStreams: false }));
  }

  static async generateInvoice(data: {
    invoiceNumber: string;
    date: string;
    clientName: string;
    items: Array<{ description: string; quantity: number; rate: number }>;
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([612, 792]); // Letter size
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    
    // Header
    page.drawText("INVOICE", {
      x: 50,
      y: 750,
      size: 24,
      font: boldFont,
      color: rgb(0.39, 0.36, 1), // Primary color
    });
    
    // Invoice details
    page.drawText(`Invoice #: ${data.invoiceNumber}`, {
      x: 50,
      y: 700,
      size: 12,
      font: font,
    });
    
    page.drawText(`Date: ${data.date}`, {
      x: 50,
      y: 680,
      size: 12,
      font: font,
    });
    
    page.drawText(`Bill To: ${data.clientName}`, {
      x: 50,
      y: 650,
      size: 12,
      font: boldFont,
    });
    
    // Items table
    let yPosition = 600;
    page.drawText("Description", { x: 50, y: yPosition, size: 12, font: boldFont });
    page.drawText("Qty", { x: 300, y: yPosition, size: 12, font: boldFont });
    page.drawText("Rate", { x: 350, y: yPosition, size: 12, font: boldFont });
    page.drawText("Total", { x: 450, y: yPosition, size: 12, font: boldFont });
    
    yPosition -= 30;
    let grandTotal = 0;
    
    data.items.forEach((item) => {
      const total = item.quantity * item.rate;
      grandTotal += total;
      
      page.drawText(item.description, { x: 50, y: yPosition, size: 10, font: font });
      page.drawText(item.quantity.toString(), { x: 300, y: yPosition, size: 10, font: font });
      page.drawText(`$${item.rate.toFixed(2)}`, { x: 350, y: yPosition, size: 10, font: font });
      page.drawText(`$${total.toFixed(2)}`, { x: 450, y: yPosition, size: 10, font: font });
      
      yPosition -= 20;
    });
    
    // Total
    yPosition -= 20;
    page.drawText(`Grand Total: $${grandTotal.toFixed(2)}`, {
      x: 350,
      y: yPosition,
      size: 14,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });
    
    return Buffer.from(await pdfDoc.save());
  }

  static async generateCertificate(data: {
    name: string;
    course: string;
    date: string;
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([792, 612]); // Landscape
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    
    // Certificate border
    page.drawRectangle({
      x: 50,
      y: 50,
      width: 692,
      height: 512,
      borderColor: rgb(0.39, 0.36, 1),
      borderWidth: 3,
    });
    
    // Title
    page.drawText("CERTIFICATE OF COMPLETION", {
      x: 200,
      y: 480,
      size: 24,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });
    
    // Content
    page.drawText("This is to certify that", {
      x: 300,
      y: 400,
      size: 16,
      font: font,
    });
    
    page.drawText(data.name, {
      x: 396 - (data.name.length * 6),
      y: 350,
      size: 20,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });
    
    page.drawText("has successfully completed", {
      x: 280,
      y: 300,
      size: 16,
      font: font,
    });
    
    page.drawText(data.course, {
      x: 396 - (data.course.length * 5),
      y: 250,
      size: 18,
      font: boldFont,
    });
    
    page.drawText(`Date: ${data.date}`, {
      x: 100,
      y: 150,
      size: 14,
      font: font,
    });
    
    return Buffer.from(await pdfDoc.save());
  }

  static async cleanupFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        console.error(`Failed to delete file ${filePath}:`, error);
      }
    }
  }
}
