import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useUser } from "@/hooks/use-user";
import { useToast } from "@/hooks/use-toast";
import { 
  CreditCard, 
  Download, 
  Calendar, 
  DollarSign, 
  AlertCircle,
  CheckCircle,
  Crown,
  RefreshCw
} from "lucide-react";

export default function Billing() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const user = userData?.user;

  const handleCancelSubscription = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Subscription cancelled",
      description: "Your subscription will remain active until the end of the current billing period.",
    });
    setIsLoading(false);
  };

  const handleReactivateSubscription = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Subscription reactivated",
      description: "Your premium features have been restored.",
    });
    setIsLoading(false);
  };

  const billingHistory = [
    {
      id: "inv_001",
      date: "2024-01-15",
      amount: "$48.00",
      status: "paid",
      description: "Annual Premium Subscription",
      downloadUrl: "#"
    },
    {
      id: "inv_002",
      date: "2023-01-15",
      amount: "$48.00",
      status: "paid",
      description: "Annual Premium Subscription",
      downloadUrl: "#"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Billing & Subscription</h1>
        <p className="text-muted-foreground">Manage your subscription and billing information</p>
      </div>

      <Tabs defaultValue="subscription" className="space-y-6">
        <TabsList>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="billing">Billing History</TabsTrigger>
          <TabsTrigger value="payment">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="subscription" className="space-y-6">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5" />
                Current Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-xl font-semibold">
                      {user?.isPremium ? "Premium" : "Free"}
                    </h3>
                    <Badge variant={user?.isPremium ? "default" : "secondary"}>
                      {user?.isPremium ? "Active" : "Current"}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground">
                    {user?.isPremium 
                      ? "Access to all premium features and unlimited processing"
                      : "Basic PDF tools with limited usage"
                    }
                  </p>
                  {user?.isPremium && (
                    <p className="text-sm text-muted-foreground mt-2">
                      Next billing date: January 15, 2025
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {user?.isPremium ? "$48" : "$0"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {user?.isPremium ? "per year" : "forever"}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex gap-3">
                {!user?.isPremium ? (
                  <Button className="bg-gradient-to-r from-primary to-secondary">
                    <Crown className="w-4 h-4 mr-2" />
                    Upgrade to Premium
                  </Button>
                ) : (
                  <>
                    <Button 
                      variant="outline" 
                      onClick={handleCancelSubscription}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <AlertCircle className="w-4 h-4 mr-2" />
                      )}
                      Cancel Subscription
                    </Button>
                    <Button variant="outline">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Update Payment Method
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Usage Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Usage This Month</CardTitle>
              <CardDescription>Track your PDF processing usage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">247</div>
                  <div className="text-sm text-muted-foreground">PDFs Processed</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {user?.isPremium ? "Unlimited" : "5/day limit"}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-secondary">1.2GB</div>
                  <div className="text-sm text-muted-foreground">Data Processed</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {user?.isPremium ? "100MB per file" : "10MB per file"}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-500">12</div>
                  <div className="text-sm text-muted-foreground">Checkout Pages</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {user?.isPremium ? "Unlimited" : "Premium feature"}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>View and download your invoices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {billingHistory.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <DollarSign className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{invoice.description}</div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(invoice.date).toLocaleDateString()} • {invoice.id}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="font-medium">{invoice.amount}</div>
                        <div className="flex items-center gap-1 text-sm">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          <span className="text-green-500">Paid</span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>Manage your payment methods</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium">•••• •••• •••• 4242</div>
                      <div className="text-sm text-muted-foreground">Expires 12/2027</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Default</Badge>
                    <Button variant="outline" size="sm">Edit</Button>
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Add Payment Method
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Billing Address</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>John Doe</div>
                <div>123 Business Street</div>
                <div>San Francisco, CA 94105</div>
                <div>United States</div>
              </div>
              <Button variant="outline" className="mt-4">
                Update Address
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
