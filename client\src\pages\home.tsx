import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Shield,
  FileImage,
  ShoppingCart,
  BarChart3,
  Settings,
  Check,
  Crown,
  Star,
  Users,
  DollarSign,
  TrendingUp,
  Eye,
  Palette,
  Mail,
  Cog
} from "lucide-react";

export default function Home() {
  const features = [
    {
      icon: FileText,
      title: "PDF Processing",
      description: "Merge, split, compress, and convert PDFs with lightning-fast processing and batch capabilities.",
      items: ["Merge multiple PDFs", "Split large documents", "Compress file sizes", "Format conversion"],
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      icon: Shield,
      title: "Security & Protection",
      description: "Add watermarks, password protection, and digital signatures to secure your documents.",
      items: ["Password protection", "Custom watermarks", "Digital signatures", "Access permissions"],
      color: "text-secondary",
      bgColor: "bg-secondary/10"
    },
    {
      icon: FileImage,
      title: "Document Generation",
      description: "Create professional invoices, certificates, and business cards with customizable templates.",
      items: ["Invoice generator", "Certificate creator", "Business card designer", "Custom templates"],
      color: "text-green-500",
      bgColor: "bg-green-500/10"
    },
    {
      icon: ShoppingCart,
      title: "Custom Checkout Pages",
      description: "Build beautiful, conversion-optimized checkout pages with multiple payment gateways.",
      items: ["Visual page builder", "Multiple payment options", "Custom branding", "Analytics tracking"],
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      icon: BarChart3,
      title: "Analytics & Insights",
      description: "Track usage, monitor performance, and optimize your document workflows with detailed analytics.",
      items: ["Usage analytics", "Performance metrics", "Processing history", "Custom reports"],
      color: "text-secondary",
      bgColor: "bg-secondary/10"
    },
    {
      icon: Settings,
      title: "Admin Dashboard",
      description: "Comprehensive admin panel for user management, customization, and system configuration.",
      items: ["User management", "Site customization", "SMTP configuration", "Payment settings"],
      color: "text-green-500",
      bgColor: "bg-green-500/10"
    }
  ];

  const stats = [
    { label: "Total Users", value: "2,847", change: "+12%", icon: Users },
    { label: "Revenue", value: "$14,250", change: "+8%", icon: DollarSign },
    { label: "PDF Processed", value: "45,892", change: "+24%", icon: FileText }
  ];

  const activities = [
    { description: "User merged 5 PDF files", time: "2 minutes ago", icon: FileText, color: "text-primary" },
    { description: "New premium subscription", time: "5 minutes ago", icon: Crown, color: "text-green-500" },
    { description: "Checkout page created", time: "12 minutes ago", icon: ShoppingCart, color: "text-secondary" }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-primary">PDFTools Pro</span>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <a href="#features" className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Tools</a>
                  <Link href="/pricing">
                    <a className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Pricing</a>
                  </Link>
                  <Link href="/about">
                    <a className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">About</a>
                  </Link>
                  <Link href="/contact">
                    <a className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-secondary/5 to-green-500/5 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center animate-fade-in">
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Professional PDF Tools
              <span className="text-primary block">Made Simple</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Merge, split, compress, convert, and secure your PDFs with our comprehensive toolkit.
              Create custom checkout pages and manage your document workflows effortlessly.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/auth">
                <Button size="lg" className="text-lg px-8 py-6">
                  Start Free Today
                </Button>
              </Link>
              <Link href="#features">
                <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                  View All Tools
                </Button>
              </Link>
            </div>
            <p className="text-sm text-muted-foreground mt-4">No credit card required • Free tier available</p>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section id="features" className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Everything You Need for PDF Management</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">Powerful tools designed for professionals, creators, and businesses of all sizes.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={feature.title} className="hover:shadow-lg transition-shadow animate-slide-up" style={{ animationDelay: `${index * 100}ms` }}>
                  <CardContent className="p-8">
                    <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-6`}>
                      <Icon className={`${feature.color} text-xl`} />
                    </div>
                    <h3 className="text-xl font-semibold text-foreground mb-4">{feature.title}</h3>
                    <p className="text-muted-foreground mb-4">{feature.description}</p>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {feature.items.map((item) => (
                        <li key={item} className="flex items-center">
                          <Check className="h-3 w-3 text-green-500 mr-2" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-24 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Simple, Transparent Pricing</h2>
            <p className="text-xl text-muted-foreground">Choose the plan that fits your needs. Upgrade or downgrade anytime.</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Tier */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-foreground mb-2">Free</h3>
                  <div className="text-4xl font-bold text-foreground mb-2">$0</div>
                  <p className="text-muted-foreground">Perfect for getting started</p>
                </div>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Basic PDF tools (merge, split)</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>10MB file size limit</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>5 operations per day</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Basic document generation</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Community support</span>
                  </li>
                </ul>
                <Link href="/auth">
                  <Button variant="outline" className="w-full">
                    Get Started Free
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Premium Tier */}
            <Card className="hover:shadow-lg transition-shadow relative border-primary">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
              </div>
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-foreground mb-2">Premium</h3>
                  <div className="text-4xl font-bold text-foreground mb-2">$48<span className="text-lg font-normal text-muted-foreground">/year</span></div>
                  <p className="text-muted-foreground">Everything you need for professional use</p>
                </div>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>All PDF tools & features</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>100MB file size limit</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Unlimited operations</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Batch processing</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Custom checkout pages</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Analytics dashboard</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>Priority support</span>
                  </li>
                  <li className="flex items-center">
                    <Check className="text-green-500 mr-3 h-4 w-4" />
                    <span>SMTP integration</span>
                  </li>
                </ul>
                <Link href="/auth">
                  <Button className="w-full">
                    Upgrade to Premium
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-8">
            <p className="text-muted-foreground">All plans include 30-day money-back guarantee</p>
          </div>
        </div>
      </section>

      {/* Dashboard Preview */}
      <section className="py-24 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Powerful Admin Dashboard</h2>
            <p className="text-xl text-muted-foreground">Complete control over your PDF toolkit and checkout pages</p>
          </div>

          <div className="bg-muted/30 rounded-2xl p-8 overflow-hidden">
            {/* Dashboard Header */}
            <Card className="mb-6">
              <CardContent className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                      <Settings className="text-white h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground">John Admin</h3>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <Button size="sm">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    New Checkout Page
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Stats Cards */}
              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  {stats.map((stat) => {
                    const Icon = stat.icon;
                    return (
                      <Card key={stat.label}>
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-muted-foreground">{stat.label}</p>
                              <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                            </div>
                            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                              <Icon className="text-primary h-5 w-5" />
                            </div>
                          </div>
                          <p className="text-sm text-green-500 mt-2 flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            {stat.change} from last month
                          </p>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>

                {/* Recent Activity */}
                <Card>
                  <CardContent className="p-6">
                    <h4 className="font-semibold text-foreground mb-4">Recent Activity</h4>
                    <div className="space-y-4">
                      {activities.map((activity, index) => {
                        const Icon = activity.icon;
                        return (
                          <div key={index} className="flex items-center space-x-4">
                            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                              <Icon className={`${activity.color} text-xs h-4 w-4`} />
                            </div>
                            <div className="flex-1">
                              <p className="text-sm text-foreground">{activity.description}</p>
                              <p className="text-xs text-muted-foreground">{activity.time}</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-6">
                    <h4 className="font-semibold text-foreground mb-4">Quick Actions</h4>
                    <div className="space-y-3">
                      <Button variant="ghost" className="w-full justify-start bg-primary/5 hover:bg-primary/10">
                        <ShoppingCart className="h-4 w-4 mr-3 text-primary" />
                        Create Checkout Page
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <Users className="h-4 w-4 mr-3" />
                        Manage Users
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <Mail className="h-4 w-4 mr-3" />
                        Configure SMTP
                      </Button>
                      <Button variant="ghost" className="w-full justify-start">
                        <Palette className="h-4 w-4 mr-3" />
                        Customize Theme
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h4 className="font-semibold text-foreground mb-4">System Status</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">API Status</span>
                        <Badge variant="secondary" className="bg-green-500 text-white">Operational</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">PDF Processing</span>
                        <Badge variant="secondary" className="bg-green-500 text-white">Healthy</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Database</span>
                        <Badge variant="secondary" className="bg-green-500 text-white">Connected</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">SMTP Servers</span>
                        <Badge variant="secondary" className="bg-orange-500 text-white">2 of 3</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Checkout Page Builder Preview */}
      <section className="py-24 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Custom Checkout Page Builder</h2>
            <p className="text-xl text-muted-foreground">Create beautiful, conversion-optimized checkout pages in minutes</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Builder Interface */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-foreground">Page Builder</h3>
                  <Button size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                </div>

                {/* Builder Content */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-3">Brand Colors</label>
                    <div className="grid grid-cols-4 gap-3">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-primary rounded-lg mx-auto mb-2 cursor-pointer border-2 border-primary"></div>
                        <span className="text-xs text-muted-foreground">Primary</span>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-secondary rounded-lg mx-auto mb-2 cursor-pointer border-2 border-transparent hover:border-gray-300"></div>
                        <span className="text-xs text-muted-foreground">Secondary</span>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-green-500 rounded-lg mx-auto mb-2 cursor-pointer border-2 border-transparent hover:border-gray-300"></div>
                        <span className="text-xs text-muted-foreground">Success</span>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 bg-foreground rounded-lg mx-auto mb-2 cursor-pointer border-2 border-transparent hover:border-gray-300"></div>
                        <span className="text-xs text-muted-foreground">Text</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-3">Payment Gateways</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" checked readOnly className="rounded text-primary mr-3" />
                        <span className="text-sm">Stripe</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" checked readOnly className="rounded text-primary mr-3" />
                        <span className="text-sm">PayPal</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" readOnly className="rounded text-primary mr-3" />
                        <span className="text-sm">Bank Transfer</span>
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Live Preview */}
            <Card>
              <div className="bg-muted p-4 border-b">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="flex-1 bg-background rounded px-4 py-1 text-xs text-muted-foreground ml-4">
                    checkout.pdftools.com/premium-plan
                  </div>
                </div>
              </div>

              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-foreground mb-2">Upgrade to Premium</h2>
                  <p className="text-muted-foreground">Unlock all features and process unlimited PDFs</p>
                </div>

                <div className="bg-muted rounded-lg p-6 mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-medium text-foreground">Premium Plan</span>
                    <span className="text-2xl font-bold text-foreground">$48/year</span>
                  </div>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-center">
                      <Check className="text-green-500 mr-2 h-4 w-4" />
                      Unlimited PDF processing
                    </li>
                    <li className="flex items-center">
                      <Check className="text-green-500 mr-2 h-4 w-4" />
                      100MB file size limit
                    </li>
                    <li className="flex items-center">
                      <Check className="text-green-500 mr-2 h-4 w-4" />
                      Priority support
                    </li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <Button className="w-full">
                    Pay with Stripe
                  </Button>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    Pay with PayPal
                  </Button>
                </div>

                <p className="text-xs text-muted-foreground text-center mt-6">
                  Secure payment • 30-day money-back guarantee
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-primary">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your PDF Workflow?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Join thousands of professionals who trust PDFTools Pro for their document processing needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
                Get Started Free
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary">
              Schedule Demo
            </Button>
          </div>
          <p className="text-white/80 text-sm mt-6">No credit card required • Upgrade anytime</p>
        </div>
      </section>

      {/* Footer */}
      <footer id="footer" className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDFTools Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with custom checkout page generation for businesses and creators.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><a href="#features" className="hover:text-white">PDF Tools</a></li>
                <li><a href="#features" className="hover:text-white">Document Generation</a></li>
                <li><a href="#features" className="hover:text-white">Checkout Builder</a></li>
                <li><a href="#features" className="hover:text-white">Analytics</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/faq"><a className="hover:text-white">FAQ</a></Link></li>
                <li><a href="#" className="hover:text-white">API Reference</a></li>
                <li><Link href="/contact"><a className="hover:text-white">Contact Support</a></Link></li>
                <li><a href="#" className="hover:text-white">Status Page</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/privacy"><a className="hover:text-white">Privacy Policy</a></Link></li>
                <li><Link href="/terms"><a className="hover:text-white">Terms of Service</a></Link></li>
                <li><Link href="/refund"><a className="hover:text-white">Refund Policy</a></Link></li>
                <li><a href="#" className="hover:text-white">Cookie Policy</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDFTools Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
