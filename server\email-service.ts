import nodemailer from 'nodemailer';
import { storage } from './storage';
import { SmtpConfig, EmailTemplate } from '@shared/schema';

export class EmailService {
  private static transporter: nodemailer.Transporter | null = null;

  // Create transporter from SMTP config
  private static async createTransporter(config: SmtpConfig): Promise<nodemailer.Transporter> {
    return nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.username,
        pass: config.password
      }
    });
  }

  // Send email using specific SMTP config
  static async sendEmail(options: {
    smtpConfigId?: number;
    userId: number;
    to: string | string[];
    subject: string;
    html?: string;
    text?: string;
    templateId?: number;
    templateData?: Record<string, any>;
  }) {
    const { smtpConfigId, userId, to, subject, html, text, templateId, templateData } = options;

    // Get SMTP configuration
    let smtpConfig: SmtpConfig | undefined;
    if (smtpConfigId) {
      const configs = await storage.getSmtpConfigs(userId);
      smtpConfig = configs.find(c => c.id === smtpConfigId);
    } else {
      smtpConfig = await storage.getDefaultSmtpConfig(userId);
    }

    if (!smtpConfig) {
      throw new Error('No SMTP configuration found');
    }

    // Create transporter
    const transporter = await this.createTransporter(smtpConfig);

    // Process template if provided
    let emailHtml = html;
    let emailText = text;
    let emailSubject = subject;

    if (templateId) {
      // Note: Email templates feature will be implemented when storage is enhanced
      console.log('Email template feature not yet implemented');
    }

    // Send email
    const mailOptions = {
      from: `${smtpConfig.fromName} <${smtpConfig.fromEmail}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject: emailSubject,
      html: emailHtml,
      text: emailText
    };

    try {
      const result = await transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Email sending failed:', error);
      throw error;
    }
  }

  // Process template variables
  private static processTemplate(template: string, data: Record<string, any>): string {
    let processed = template;
    
    // Replace variables like {{variableName}}
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processed = processed.replace(regex, String(value));
    }
    
    return processed;
  }

  // Test SMTP configuration
  static async testSmtpConfig(config: SmtpConfig): Promise<boolean> {
    try {
      const transporter = await this.createTransporter(config);
      await transporter.verify();
      return true;
    } catch (error) {
      console.error('SMTP test failed:', error);
      return false;
    }
  }

  // Send payment confirmation email
  static async sendPaymentConfirmation(userId: number, paymentData: {
    amount: string;
    currency: string;
    gateway: string;
    transactionId: string;
    customerEmail: string;
    checkoutPageId?: number;
  }) {
    // Get routing rules for checkout page
    let smtpConfigId: number | undefined;
    
    if (paymentData.checkoutPageId) {
      const configs = await storage.getSmtpConfigs(userId);
      for (const config of configs) {
        if (config.routingRules) {
          const rules = config.routingRules as any;
          if (rules.checkoutPages && rules.checkoutPages.includes(paymentData.checkoutPageId)) {
            smtpConfigId = config.id;
            break;
          }
        }
      }
    }

    await this.sendEmail({
      smtpConfigId,
      userId,
      to: paymentData.customerEmail,
      subject: 'Payment Confirmation - PDF Tools Service',
      html: `
        <h2>Payment Confirmed</h2>
        <p>Thank you for your payment!</p>
        <div>
          <strong>Transaction Details:</strong><br>
          Amount: ${paymentData.amount} ${paymentData.currency}<br>
          Gateway: ${paymentData.gateway}<br>
          Transaction ID: ${paymentData.transactionId}
        </div>
        <p>Your premium features are now active.</p>
      `,
      text: `Payment Confirmed\n\nThank you for your payment!\n\nAmount: ${paymentData.amount} ${paymentData.currency}\nGateway: ${paymentData.gateway}\nTransaction ID: ${paymentData.transactionId}\n\nYour premium features are now active.`
    });
  }

  // Send subscription renewal reminder
  static async sendSubscriptionReminder(userId: number, userEmail: string, expiresAt: Date) {
    await this.sendEmail({
      userId,
      to: userEmail,
      subject: 'Subscription Renewal Reminder - PDF Tools Service',
      html: `
        <h2>Subscription Renewal Reminder</h2>
        <p>Your premium subscription expires on ${expiresAt.toLocaleDateString()}.</p>
        <p>Renew now to continue enjoying premium features!</p>
        <a href="${process.env.BASE_URL}/subscribe" style="background-color: #635BFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Renew Subscription</a>
      `,
      text: `Subscription Renewal Reminder\n\nYour premium subscription expires on ${expiresAt.toLocaleDateString()}.\n\nRenew now to continue enjoying premium features!\n\nRenew at: ${process.env.BASE_URL}/subscribe`
    });
  }
}