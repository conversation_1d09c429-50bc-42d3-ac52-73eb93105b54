import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Link } from "wouter";
import { 
  FileText, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Crown,
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useUser } from "@/hooks/use-user";

export default function Dashboard() {
  const { data: userData } = useUser();
  const user = userData?.user;

  const { data: analytics } = useQuery({
    queryKey: ["/api/analytics"],
    enabled: !!user,
  });

  const { data: operations } = useQuery({
    queryKey: ["/api/operations"],
    enabled: !!user,
  });

  if (!analytics || !operations) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  const isAdmin = user?.role === "admin";
  const usageLimit = user?.isPremium ? 100 : 5;
  const currentUsage = operations.filter((op: any) => {
    const today = new Date();
    const opDate = new Date(op.createdAt);
    return opDate.toDateString() === today.toDateString();
  }).length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-green-500";
      case "failed": return "text-red-500";
      default: return "text-yellow-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return CheckCircle;
      case "failed": return XCircle;
      default: return Clock;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome back, {user?.username}!
          </h1>
          <p className="text-muted-foreground">
            {isAdmin ? "Admin Dashboard" : "Your PDF processing workspace"}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge variant={user?.isPremium ? "default" : "secondary"}>
            {user?.isPremium ? "Premium" : "Free"}
          </Badge>
          {!user?.isPremium && (
            <Link href="/subscribe">
              <Button className="bg-gradient-to-r from-primary to-secondary">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Usage Limits */}
      {!isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Daily Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>PDF Operations</span>
                <span>{currentUsage} / {usageLimit}</span>
              </div>
              <Progress 
                value={(currentUsage / usageLimit) * 100} 
                className="h-2"
              />
              <p className="text-xs text-muted-foreground">
                {usageLimit - currentUsage} operations remaining today
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {isAdmin ? "Total Users" : "Your PDFs Processed"}
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {isAdmin ? analytics.totalUsers : analytics.totalPdfProcessed}
                </p>
              </div>
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                {isAdmin ? <Users className="text-primary h-5 w-5" /> : <FileText className="text-primary h-5 w-5" />}
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +24% from last month
            </p>
          </CardContent>
        </Card>

        {isAdmin && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Revenue</p>
                  <p className="text-2xl font-bold text-foreground">${analytics.totalRevenue}</p>
                </div>
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <DollarSign className="text-green-500 h-5 w-5" />
                </div>
              </div>
              <p className="text-sm text-green-500 mt-2 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                +8% from last month
              </p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  {isAdmin ? "Total PDF Processed" : "Success Rate"}
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {isAdmin 
                    ? analytics.totalPdfProcessed 
                    : `${Math.round((operations.filter((op: any) => op.status === 'completed').length / Math.max(operations.length, 1)) * 100)}%`
                  }
                </p>
              </div>
              <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                <FileText className="text-secondary h-5 w-5" />
              </div>
            </div>
            <p className="text-sm text-green-500 mt-2 flex items-center">
              <TrendingUp className="h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link href="/tools">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground">PDF Tools</h3>
              <p className="text-sm text-muted-foreground">Process PDFs</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/checkout-builder">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <ShoppingCart className="h-8 w-8 text-secondary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground">Checkout Builder</h3>
              <p className="text-sm text-muted-foreground">Create pages</p>
            </CardContent>
          </Card>
        </Link>

        {!user?.isPremium && (
          <Link href="/subscribe">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer border-primary/20">
              <CardContent className="p-6 text-center">
                <Crown className="h-8 w-8 text-primary mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Upgrade</h3>
                <p className="text-sm text-muted-foreground">Go Premium</p>
              </CardContent>
            </Card>
          </Link>
        )}

        {isAdmin && (
          <Link href="/admin">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Crown className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <h3 className="font-semibold text-foreground">Admin Panel</h3>
                <p className="text-sm text-muted-foreground">Manage system</p>
              </CardContent>
            </Card>
          </Link>
        )}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.recentOperations.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">No recent activity</p>
            ) : (
              analytics.recentOperations.map((operation: any) => {
                const StatusIcon = getStatusIcon(operation.status);
                return (
                  <div key={operation.id} className="flex items-center space-x-4 p-3 rounded-lg bg-muted/30">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${operation.status === 'completed' ? 'bg-green-500/10' : operation.status === 'failed' ? 'bg-red-500/10' : 'bg-yellow-500/10'}`}>
                      <StatusIcon className={`h-4 w-4 ${getStatusColor(operation.status)}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">
                        {operation.operation.charAt(0).toUpperCase() + operation.operation.slice(1)} - {operation.fileName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(operation.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <Badge variant={operation.status === 'completed' ? 'default' : operation.status === 'failed' ? 'destructive' : 'secondary'}>
                      {operation.status}
                    </Badge>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
