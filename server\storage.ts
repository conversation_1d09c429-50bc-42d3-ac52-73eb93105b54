import { 
  users, 
  pdfOperations, 
  checkoutPages, 
  smtpConfigs, 
  siteConfig, 
  payments,
  type User, 
  type InsertUser,
  type PdfOperation,
  type InsertPdfOperation,
  type CheckoutPage,
  type InsertCheckoutPage,
  type SmtpConfig,
  type InsertSmtpConfig,
  type SiteConfig,
  type InsertSiteConfig,
  type Payment,
  type InsertPayment
} from "@shared/schema";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserPremiumStatus(id: number, isPremium: boolean): Promise<User>;
  updateUserStripeInfo(id: number, customerId: string, subscriptionId: string): Promise<User>;
  
  createPdfOperation(operation: InsertPdfOperation & { userId: number }): Promise<PdfOperation>;
  getUserPdfOperations(userId: number): Promise<PdfOperation[]>;
  updatePdfOperationStatus(id: number, status: string): Promise<void>;
  
  createCheckoutPage(page: InsertCheckoutPage & { userId: number }): Promise<CheckoutPage>;
  getCheckoutPages(userId: number): Promise<CheckoutPage[]>;
  getCheckoutPageBySlug(slug: string): Promise<CheckoutPage | undefined>;
  updateCheckoutPage(id: number, updates: Partial<CheckoutPage>): Promise<CheckoutPage>;
  deleteCheckoutPage(id: number): Promise<void>;
  
  createSmtpConfig(config: InsertSmtpConfig & { userId: number }): Promise<SmtpConfig>;
  getSmtpConfigs(userId: number): Promise<SmtpConfig[]>;
  getDefaultSmtpConfig(userId: number): Promise<SmtpConfig | undefined>;
  updateSmtpConfig(id: number, updates: Partial<SmtpConfig>): Promise<SmtpConfig>;
  deleteSmtpConfig(id: number): Promise<void>;
  
  getSiteConfig(key: string): Promise<SiteConfig | undefined>;
  setSiteConfig(config: InsertSiteConfig): Promise<SiteConfig>;
  
  createPayment(payment: InsertPayment & { userId?: number }): Promise<Payment>;
  getPayments(userId?: number): Promise<Payment[]>;
  updatePaymentStatus(id: number, status: string, gatewayTransactionId?: string): Promise<Payment>;
  
  getAnalytics(userId?: number): Promise<{
    totalUsers: number;
    totalRevenue: number;
    totalPdfProcessed: number;
    recentOperations: PdfOperation[];
  }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private pdfOperations: Map<number, PdfOperation>;
  private checkoutPages: Map<number, CheckoutPage>;
  private smtpConfigs: Map<number, SmtpConfig>;
  private siteConfigs: Map<string, SiteConfig>;
  private payments: Map<number, Payment>;
  private currentId: number;

  constructor() {
    this.users = new Map();
    this.pdfOperations = new Map();
    this.checkoutPages = new Map();
    this.smtpConfigs = new Map();
    this.siteConfigs = new Map();
    this.payments = new Map();
    this.currentId = 1;
    
    // Create default admin user
    this.initializeDefaultAdmin();
  }

  private async initializeDefaultAdmin() {
    // In a real implementation, you'd import bcrypt here
    // For now, using a simple hash simulation
    const hashedPassword = "hashed_admin123"; // This would be: await bcrypt.hash("admin123", 10);
    const admin: User = {
      id: this.currentId++,
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      role: "admin",
      isPremium: true,
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      createdAt: new Date(),
    };
    this.users.set(admin.id, admin);
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // In a real implementation: const hashedPassword = await bcrypt.hash(insertUser.password, 10);
    const hashedPassword = `hashed_${insertUser.password}`;
    const user: User = {
      ...insertUser,
      id: this.currentId++,
      password: hashedPassword,
      role: "user",
      isPremium: false,
      stripeCustomerId: null,
      stripeSubscriptionId: null,
      createdAt: new Date(),
    };
    this.users.set(user.id, user);
    return user;
  }

  async updateUserPremiumStatus(id: number, isPremium: boolean): Promise<User> {
    const user = this.users.get(id);
    if (!user) throw new Error("User not found");
    
    const updatedUser = { ...user, isPremium };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async updateUserStripeInfo(id: number, customerId: string, subscriptionId: string): Promise<User> {
    const user = this.users.get(id);
    if (!user) throw new Error("User not found");
    
    const updatedUser = { 
      ...user, 
      stripeCustomerId: customerId, 
      stripeSubscriptionId: subscriptionId,
      isPremium: true 
    };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async createPdfOperation(operation: InsertPdfOperation & { userId: number }): Promise<PdfOperation> {
    const pdfOp: PdfOperation = {
      ...operation,
      id: this.currentId++,
      status: "processing",
      createdAt: new Date(),
    };
    this.pdfOperations.set(pdfOp.id, pdfOp);
    return pdfOp;
  }

  async getUserPdfOperations(userId: number): Promise<PdfOperation[]> {
    return Array.from(this.pdfOperations.values()).filter(op => op.userId === userId);
  }

  async updatePdfOperationStatus(id: number, status: string): Promise<void> {
    const operation = this.pdfOperations.get(id);
    if (operation) {
      this.pdfOperations.set(id, { ...operation, status });
    }
  }

  async createCheckoutPage(page: InsertCheckoutPage & { userId: number }): Promise<CheckoutPage> {
    const checkoutPage: CheckoutPage = {
      ...page,
      id: this.currentId++,
      isActive: true,
      createdAt: new Date(),
    };
    this.checkoutPages.set(checkoutPage.id, checkoutPage);
    return checkoutPage;
  }

  async getCheckoutPages(userId: number): Promise<CheckoutPage[]> {
    return Array.from(this.checkoutPages.values()).filter(page => page.userId === userId);
  }

  async getCheckoutPageBySlug(slug: string): Promise<CheckoutPage | undefined> {
    return Array.from(this.checkoutPages.values()).find(page => page.slug === slug);
  }

  async updateCheckoutPage(id: number, updates: Partial<CheckoutPage>): Promise<CheckoutPage> {
    const page = this.checkoutPages.get(id);
    if (!page) throw new Error("Checkout page not found");
    
    const updatedPage = { ...page, ...updates };
    this.checkoutPages.set(id, updatedPage);
    return updatedPage;
  }

  async deleteCheckoutPage(id: number): Promise<void> {
    this.checkoutPages.delete(id);
  }

  async createSmtpConfig(config: InsertSmtpConfig & { userId: number }): Promise<SmtpConfig> {
    const smtpConfig: SmtpConfig = {
      ...config,
      id: this.currentId++,
      createdAt: new Date(),
    };
    this.smtpConfigs.set(smtpConfig.id, smtpConfig);
    return smtpConfig;
  }

  async getSmtpConfigs(userId: number): Promise<SmtpConfig[]> {
    return Array.from(this.smtpConfigs.values()).filter(config => config.userId === userId);
  }

  async getDefaultSmtpConfig(userId: number): Promise<SmtpConfig | undefined> {
    return Array.from(this.smtpConfigs.values()).find(
      config => config.userId === userId && config.isDefault
    );
  }

  async updateSmtpConfig(id: number, updates: Partial<SmtpConfig>): Promise<SmtpConfig> {
    const config = this.smtpConfigs.get(id);
    if (!config) throw new Error("SMTP config not found");
    
    const updatedConfig = { ...config, ...updates };
    this.smtpConfigs.set(id, updatedConfig);
    return updatedConfig;
  }

  async deleteSmtpConfig(id: number): Promise<void> {
    this.smtpConfigs.delete(id);
  }

  async getSiteConfig(key: string): Promise<SiteConfig | undefined> {
    return this.siteConfigs.get(key);
  }

  async setSiteConfig(config: InsertSiteConfig): Promise<SiteConfig> {
    const siteConfig: SiteConfig = {
      ...config,
      id: this.currentId++,
      updatedAt: new Date(),
    };
    this.siteConfigs.set(config.key, siteConfig);
    return siteConfig;
  }

  async createPayment(payment: InsertPayment & { userId?: number }): Promise<Payment> {
    const paymentRecord: Payment = {
      ...payment,
      id: this.currentId++,
      status: "pending",
      createdAt: new Date(),
    };
    this.payments.set(paymentRecord.id, paymentRecord);
    return paymentRecord;
  }

  async getPayments(userId?: number): Promise<Payment[]> {
    const allPayments = Array.from(this.payments.values());
    if (userId) {
      return allPayments.filter(payment => payment.userId === userId);
    }
    return allPayments;
  }

  async updatePaymentStatus(id: number, status: string, gatewayTransactionId?: string): Promise<Payment> {
    const payment = this.payments.get(id);
    if (!payment) throw new Error("Payment not found");
    
    const updatedPayment = { 
      ...payment, 
      status, 
      gatewayTransactionId: gatewayTransactionId || payment.gatewayTransactionId 
    };
    this.payments.set(id, updatedPayment);
    return updatedPayment;
  }

  async getAnalytics(userId?: number): Promise<{
    totalUsers: number;
    totalRevenue: number;
    totalPdfProcessed: number;
    recentOperations: PdfOperation[];
  }> {
    const totalUsers = this.users.size;
    const completedPayments = Array.from(this.payments.values()).filter(p => p.status === "completed");
    const totalRevenue = completedPayments.reduce((sum, p) => sum + parseFloat(p.amount.toString()), 0);
    
    const allOperations = Array.from(this.pdfOperations.values());
    const totalPdfProcessed = userId 
      ? allOperations.filter(op => op.userId === userId).length 
      : allOperations.length;
    
    const recentOperations = allOperations
      .filter(op => userId ? op.userId === userId : true)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    return {
      totalUsers,
      totalRevenue,
      totalPdfProcessed,
      recentOperations,
    };
  }
}

export const storage = new MemStorage();
